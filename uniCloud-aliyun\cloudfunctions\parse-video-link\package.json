{"name": "parse-video-link", "version": "3.0.0", "description": "基于parse-video-py项目实现的多平台视频解析云函数，支持抖音和小红书视频/图集解析", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1"}, "dependencies": {"uni-config-center": "file:../../../src/uni_modules/uni-config-center/uniCloud/cloudfunctions/common/uni-config-center"}, "keywords": ["video", "parse", "do<PERSON><PERSON>", "redbook", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "watermark-removal", "image-gallery", "video-parser", "multi-platform"], "author": "", "license": "ISC", "extensions": {}}