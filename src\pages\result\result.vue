<template>
  <view class="result-container">
    <!-- 成功提示 -->
    <view class="success-card">
      <view class="success-icon">✅</view>
      <text class="success-title">处理完成</text>
      <text class="success-desc">您的视频已成功生成字幕</text>
    </view>

    <!-- 视频预览 -->
    <view class="video-card">
      <view class="card-header">
        <text class="card-title">视频预览</text>
      </view>
      <view class="video-preview">
        <video v-if="videoUrl" :src="videoUrl" controls class="video-player"></video>
        <view v-else class="video-placeholder">
          <text class="video-icon">🎬</text>
          <text class="video-text">视频加载中...</text>
        </view>
      </view>
    </view>

    <!-- 字幕预览 -->
    <view class="subtitle-card">
      <view class="card-header">
        <text class="card-title">字幕预览</text>
      </view>
      <view class="subtitle-content">
        <view class="subtitle-item" v-for="(item, index) in subtitles" :key="index">
          <text class="subtitle-time">{{ item.startTime }} - {{ item.endTime }}</text>
          <text class="subtitle-text">{{ item.text }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button @click="downloadVideo" class="btn btn-primary">
        <text class="btn-icon">📥</text>
        <text>下载视频</text>
      </button>
    </view>

    <!-- 底部空间 -->
    <view class="bottom-space"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'

// 字幕数据
const subtitles = ref([])
const taskId = ref('')
const videoUrl = ref('')

onMounted(() => {
  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  if (options.taskId) {
    taskId.value = options.taskId
    console.log('接收到任务ID:', taskId.value)

    // 加载处理结果
    loadResult()
  } else {
    uni.showModal({
      title: '参数错误',
      content: '缺少任务ID参数，请重新处理视频',
      showCancel: false,
      success: () => {
        uni.navigateBack()
      }
    })
  }
})

// 加载处理结果
const loadResult = async () => {
  try {
    uni.showLoading({
      title: '加载中...'
    })

    // 调用云函数获取处理结果
    const result = await uniCloud.callFunction({
      name: 'get-task-result',
      data: {
        taskId: taskId.value
      }
    })

    console.log('处理结果:', result)

    if (result.result.code === 200) {
      const { videoUrl: resultVideoUrl, subtitles: resultSubtitles } = result.result.data

      videoUrl.value = resultVideoUrl || ''
      subtitles.value = resultSubtitles || []

      uni.hideLoading()
    } else {
      uni.hideLoading()
      uni.showToast({
        title: result.result.message || '加载失败',
        icon: 'none'
      })
    }

  } catch (error) {
    console.error('加载处理结果失败:', error)
    uni.hideLoading()
    uni.showToast({
      title: '加载失败',
      icon: 'none'
    })
  }
}

// 下载视频
const downloadVideo = async () => {
  if (!videoUrl.value) {
    uni.showToast({
      title: '视频文件不存在',
      icon: 'none'
    })
    return
  }

  try {
    uni.showLoading({
      title: '下载中...'
    })

    // 下载视频文件
    uni.downloadFile({
      url: videoUrl.value,
      success: (res) => {
        if (res.statusCode === 200) {
          // 保存到相册
          uni.saveVideoToPhotosAlbum({
            filePath: res.tempFilePath,
            success: () => {
              uni.hideLoading()
              uni.showToast({
                title: '保存成功',
                icon: 'success'
              })
            },
            fail: (err) => {
              console.error('保存视频失败:', err)
              uni.hideLoading()
              uni.showToast({
                title: '保存失败',
                icon: 'none'
              })
            }
          })
        }
      },
      fail: (err) => {
        console.error('下载视频失败:', err)
        uni.hideLoading()
        uni.showToast({
          title: '下载失败',
          icon: 'none'
        })
      }
    })

  } catch (error) {
    console.error('下载视频出错:', error)
    uni.hideLoading()
    uni.showToast({
      title: '下载出错',
      icon: 'none'
    })
  }
}
</script>

<style scoped>
.result-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  padding: 32rpx;
  padding-bottom: 120rpx;
}

.success-card {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  text-align: center;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
}

.success-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  display: block;
}

.success-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.success-desc {
  display: block;
  font-size: 28rpx;
  color: #6b7280;
  line-height: 1.5;
}

.video-card, .subtitle-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
}

.card-header {
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.video-preview {
  aspect-ratio: 16/9;
  background: #f3f4f6;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.video-player {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

.video-placeholder {
  text-align: center;
}

.video-icon {
  font-size: 64rpx;
  display: block;
  margin-bottom: 16rpx;
}

.video-text {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.video-desc {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
}

.subtitle-content {
  max-height: 400rpx;
  overflow-y: auto;
}

.subtitle-item {
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f3f4f6;
}

.subtitle-item:last-child {
  border-bottom: none;
}

.subtitle-time {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  margin-bottom: 8rpx;
}

.subtitle-text {
  display: block;
  font-size: 28rpx;
  color: #1f2937;
  line-height: 1.5;
}

.action-section {
  display: flex;
  justify-content: center;
  margin-bottom: 24rpx;
}

.btn {
  padding: 24rpx 32rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  min-width: 200rpx;
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  width: 100%;
}

.btn-icon {
  font-size: 28rpx;
}

.bottom-space {
  height: 80rpx;
}
</style>
