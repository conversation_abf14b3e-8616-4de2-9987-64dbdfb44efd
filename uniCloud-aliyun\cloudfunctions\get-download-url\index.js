// uniCloud云函数：生成下载URL
"use strict";

const OSS = require("ali-oss");
const createConfig = require("uni-config-center");

/**
 * 生成视频文件的下载URL
 *
 * @param {Object} event
 * @param {string} event.taskId - 任务ID（必需）
 * @param {string} event.openid - 用户openid（用于权限验证，可选）
 * @returns {Object} 下载URL信息
 */
exports.main = async (event, context) => {
  try {
    const { taskId, openid } = event;

    console.log("get-download-url 云函数被调用，参数：", { taskId, hasOpenid: !!openid });

    // 参数验证
    if (!taskId) {
      console.error("参数验证失败：缺少taskId参数");
      return {
        errCode: "INVALID_PARAM",
        errMsg: "缺少必要参数：taskId",
      };
    }

    // 验证taskId格式（简单验证）
    if (typeof taskId !== "string" || taskId.length === 0) {
      console.error("参数验证失败：taskId格式不正确");
      return {
        errCode: "INVALID_PARAM",
        errMsg: "taskId格式不正确",
      };
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    console.log("准备查询任务信息，taskId：", taskId);

    // 查询任务信息
    let taskQuery = tasksCollection.where({
      _id: taskId,
    });

    // 如果提供了openid，添加用户权限验证
    if (openid) {
      // 首先查询用户ID
      const usersCollection = db.collection("users");
      const userResult = await usersCollection
        .where({
          openid: openid,
        })
        .field({
          _id: true,
        })
        .limit(1)
        .get();

      if (!userResult.data || userResult.data.length === 0) {
        console.error("用户验证失败：用户不存在", openid);
        return {
          errCode: "USER_NOT_FOUND",
          errMsg: "用户不存在",
        };
      }

      const userId = userResult.data[0]._id;
      console.log("用户验证通过，userId：", userId);

      // 添加用户权限过滤
      taskQuery = taskQuery.where({
        userId: userId,
      });
    }

    // 查询任务信息
    const taskResult = await taskQuery
      .field({
        _id: true,
        status: true,
        finalVideoUrl: true,
        subtitleOssUrl: true,
        fileName: true,
      })
      .limit(1)
      .get();

    if (!taskResult.data || taskResult.data.length === 0) {
      console.error("任务查询失败：任务不存在或无权限访问", taskId);
      return {
        errCode: "TASK_NOT_FOUND",
        errMsg: "任务不存在或无权限访问",
      };
    }

    const taskData = taskResult.data[0];
    console.log("任务查询成功，状态：", taskData.status);

    // 检查任务状态
    if (taskData.status !== "completed") {
      console.error("任务未完成，当前状态：", taskData.status);
      return {
        errCode: "TASK_NOT_COMPLETED",
        errMsg: "任务尚未完成，无法生成下载链接",
        data: {
          status: taskData.status,
        },
      };
    }

    // 检查是否有最终视频文件
    if (!taskData.finalVideoUrl) {
      console.error("任务已完成但缺少最终视频文件");
      return {
        errCode: "VIDEO_NOT_FOUND",
        errMsg: "视频文件不存在",
      };
    }

    // 获取阿里云OSS配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-oss",
      defaultConfig: {
        region: "oss-cn-shanghai",
        bucket: "video--tanslate",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const region = aliyunConfig.config("region");
    const bucketName = aliyunConfig.config("bucket");

    // 验证配置
    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云OSS配置缺失");
    }

    // 初始化OSS客户端
    const client = new OSS({
      accessKeyId: accessKeyId,
      accessKeySecret: accessKeySecret,
      bucket: bucketName,
      region: region,
    });

    // 从finalVideoUrl中提取objectKey
    const videoObjectKey = extractObjectKeyFromUrl(taskData.finalVideoUrl);
    if (!videoObjectKey) {
      throw new Error("无法解析视频文件路径");
    }

    console.log("生成视频下载URL，objectKey：", videoObjectKey);

    // 生成带时效性的签名URL（有效期1小时）
    const videoDownloadUrl = client.signatureUrl(videoObjectKey, {
      expires: 3600, // 1小时
      response: {
        "content-disposition": `attachment; filename="${taskData.fileName || "video.mp4"}"`,
      },
    });

    console.log("视频下载URL生成成功");

    // 构建返回数据
    const responseData = {
      taskId: taskData._id,
      videoDownloadUrl: videoDownloadUrl,
      fileName: taskData.fileName || "video.mp4",
      expiresIn: 3600, // 有效期（秒）
    };

    // 如果有字幕文件，也生成字幕下载URL
    if (taskData.subtitleOssUrl) {
      const subtitleObjectKey = extractObjectKeyFromUrl(taskData.subtitleOssUrl);
      if (subtitleObjectKey) {
        const subtitleDownloadUrl = client.signatureUrl(subtitleObjectKey, {
          expires: 3600, // 1小时
          response: {
            "content-disposition": `attachment; filename="${taskData.fileName?.replace(/\.[^.]+$/, ".srt") || "subtitle.srt"}"`,
          },
        });

        responseData.subtitleDownloadUrl = subtitleDownloadUrl;
        console.log("字幕下载URL生成成功");
      }
    }

    return {
      errCode: 0,
      code: 200,
      errMsg: "生成下载链接成功",
      data: responseData,
    };
  } catch (error) {
    console.error("get-download-url 云函数执行错误：", error);

    return {
      errCode: "GET_DOWNLOAD_URL_FAILED",
      code: 500,
      errMsg: "生成下载链接失败: " + error.message,
    };
  }
};

/**
 * 从OSS URL中提取objectKey
 * @param {string} ossUrl - OSS完整URL
 * @returns {string|null} objectKey
 */
function extractObjectKeyFromUrl(ossUrl) {
  try {
    // 匹配OSS URL格式：https://bucket.region.aliyuncs.com/objectKey
    const match = ossUrl.match(/https:\/\/[^.]+\.[^.]+\.aliyuncs\.com\/(.+)/);
    return match ? match[1] : null;
  } catch (error) {
    console.error("解析OSS URL失败：", error);
    return null;
  }
}
