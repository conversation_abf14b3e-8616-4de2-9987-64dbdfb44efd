// uniCloud云函数：定时轮询Paraformer识别任务状态
"use strict";

/**
 * 定时轮询Paraformer识别任务状态
 * 支持定时批量处理和按需查询两种模式
 *
 * @param {Object} event
 * @param {string} event.taskId - 可选，指定要查询的任务ID（用于前端轮询）
 * @param {string} event.mode - 查询模式：'batch'(批量处理，定时器调用) 或 'query'(查询指定任务，前端调用)
 * @returns {Object} 处理结果
 */
exports.main = async (event, context) => {
  try {
    console.log("poll-paraformer-tasks 云函数被调用，参数：", event);

    const { taskId, mode = 'batch' } = event;
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    // 判断调用模式
    if (mode === 'query' && taskId) {
      // 前端查询指定任务模式
      console.log(`查询指定任务 ${taskId} 的状态`);

      const taskInfo = await tasksCollection.doc(taskId).get();
      if (!taskInfo.data || taskInfo.data.length === 0) {
        return {
          code: 404,
          message: "任务不存在",
          data: { taskId: taskId }
        };
      }

      const task = taskInfo.data[0];

      // 如果任务没有 paraformerTaskId，说明还未到语音识别阶段
      if (!task.paraformerTaskId) {
        return {
          code: 200,
          message: "任务尚未开始语音识别",
          data: {
            taskId: taskId,
            status: task.status,
            message: "任务尚未开始语音识别阶段"
          }
        };
      }

      // 执行单次检查
      const checkResult = await checkSingleTask(task);

      return {
        code: 200,
        message: "查询完成",
        data: checkResult
      };

    } else {
      // 定时批量处理模式
      console.log("开始批量处理所有待识别的任务");

      // 查询所有状态为 "recognizing" 且有 paraformerTaskId 的任务
      const pendingTasksResult = await tasksCollection
        .where({
          status: "recognizing",
          paraformerTaskId: db.command.exists(true)
        })
        .get();

      const pendingTasks = pendingTasksResult.data || [];
      console.log(`找到 ${pendingTasks.length} 个待处理的识别任务`);

      if (pendingTasks.length === 0) {
        return {
          code: 200,
          message: "没有待处理的任务",
          data: {
            processedCount: 0,
            results: []
          }
        };
      }

      // 批量处理任务
      const results = await batchProcessTasks(pendingTasks);

      return {
        code: 200,
        message: `批量处理完成，共处理 ${pendingTasks.length} 个任务`,
        data: {
          processedCount: pendingTasks.length,
          results: results
        }
      };
    }

  } catch (error) {
    console.error("poll-paraformer-tasks 云函数执行错误：", error);

    return {
      code: 500,
      message: "任务处理失败: " + error.message,
    };
  }
};

/**
 * 批量处理待识别的任务
 * @param {Array} tasks - 待处理的任务列表
 * @returns {Promise<Array>} 处理结果列表
 */
async function batchProcessTasks(tasks) {
  const results = [];

  console.log(`开始批量处理 ${tasks.length} 个任务`);

  // 并发处理任务，但限制并发数量避免过载
  const concurrencyLimit = 5; // 最多同时处理5个任务
  const chunks = [];

  for (let i = 0; i < tasks.length; i += concurrencyLimit) {
    chunks.push(tasks.slice(i, i + concurrencyLimit));
  }

  for (const chunk of chunks) {
    const chunkPromises = chunk.map(async (task) => {
      try {
        console.log(`处理任务 ${task._id}`);
        const result = await checkSingleTask(task);
        return {
          taskId: task._id,
          success: true,
          result: result
        };
      } catch (error) {
        console.error(`处理任务 ${task._id} 失败：`, error);
        return {
          taskId: task._id,
          success: false,
          error: error.message
        };
      }
    });

    const chunkResults = await Promise.all(chunkPromises);
    results.push(...chunkResults);

    // 在处理下一批之前稍作延迟，避免API调用过于频繁
    if (chunks.indexOf(chunk) < chunks.length - 1) {
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  console.log(`批量处理完成，成功: ${results.filter(r => r.success).length}，失败: ${results.filter(r => !r.success).length}`);

  return results;
}

/**
 * 检查单个任务的状态
 * @param {Object} task - 任务对象
 * @returns {Promise<Object>} 检查结果
 */
async function checkSingleTask(task) {
  try {
    console.log(`检查任务 ${task._id}，Paraformer TaskId: ${task.paraformerTaskId}`);

    // 获取阿里云DashScope配置
    const createConfig = require("uni-config-center");
    const aliyunConfig = createConfig({
      pluginId: "aliyun-dashscope",
      defaultConfig: {
        endpoint: "https://dashscope.aliyuncs.com",
      },
    });

    const apiKey = aliyunConfig.config("apiKey");
    const endpoint = aliyunConfig.config("endpoint");

    if (!apiKey) {
      throw new Error("阿里云DashScope配置缺失");
    }

    const response = await queryParaformerTask(apiKey, endpoint, task.paraformerTaskId);
    console.log("识别后的返回的值", response);
    console.log(`任务 ${task._id} Paraformer状态：`, response.output.task_status);

    const taskStatus = response.output.task_status;
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    if (taskStatus === "SUCCEEDED") {
      // 识别成功，需要下载结果并处理
      console.log(`任务 ${task._id} 识别成功，开始处理结果`);

      const transcriptionResult = await downloadTranscriptionResult(response.output.results);

      const processResult = await uniCloud.callFunction({
        name: "process-video-task",
        data: {
          taskId: task._id,
          action: "handle_paraformer_success",
          paraformerResult: transcriptionResult
        }
      });

      return {
        taskId: task._id,
        status: "success",
        message: "识别成功并已处理",
        processResult: processResult.result
      };

    } else if (taskStatus === "FAILED") {
      // 识别失败
      console.error(`任务 ${task._id} 识别失败`);

      await tasksCollection.doc(task._id).update({
        status: "failed",
        errorMessage: "Paraformer识别失败：" + taskStatus,
        updateTime: new Date(),
      });

      return {
        taskId: task._id,
        status: "failed",
        message: "Paraformer识别失败"
      };

    } else {
      // 仍在进行中
      return {
        taskId: task._id,
        status: "running",
        message: `Paraformer状态：${taskStatus}`
      };
    }

  } catch (error) {
    console.error(`检查任务 ${task._id} 失败：`, error);
    return {
      taskId: task._id,
      status: "error",
      message: "检查失败：" + error.message
    };
  }
}

/**
 * 查询Paraformer语音识别任务状态
 * @param {string} apiKey - API密钥
 * @param {string} endpoint - API端点
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 任务状态和结果
 */
async function queryParaformerTask(apiKey, endpoint, taskId) {
  const https = require('https');
  const http = require('http');
  const url = require('url');

  const parsedUrl = url.parse(`${endpoint}/api/v1/tasks/${taskId}`);
  
  const options = {
    hostname: parsedUrl.hostname,
    port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
    path: parsedUrl.path,
    method: 'GET',
    headers: {
      'Authorization': `Bearer ${apiKey}`,
      'Content-Type': 'application/json'
    }
  };

  return new Promise((resolve, reject) => {
    const client = parsedUrl.protocol === 'https:' ? https : http;
    
    const req = client.request(options, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const response = JSON.parse(data);
          
          if (res.statusCode === 200) {
            resolve(response);
          } else {
            console.error("查询Paraformer任务失败：", response);
            reject(new Error(`查询任务失败: ${JSON.stringify(response)}`));
          }
        } catch (error) {
          console.error("解析Paraformer查询响应失败：", error);
          reject(new Error(`解析响应失败: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      console.error("Paraformer查询请求失败：", error);
      reject(new Error(`请求失败: ${error.message}`));
    });
    
    req.end();
  });
}

/**
 * 下载转录结果
 * @param {Array} results - 结果数组
 * @returns {Promise<Object>} 转录结果
 */
async function downloadTranscriptionResult(results) {
  if (!results || results.length === 0) {
    throw new Error("没有找到转录结果");
  }
  
  const firstResult = results[0];
  if (firstResult.subtask_status !== "SUCCEEDED") {
    throw new Error(`转录任务失败: ${firstResult.subtask_status}`);
  }
  
  const transcriptionUrl = firstResult.transcription_url;
  if (!transcriptionUrl) {
    throw new Error("没有找到转录结果URL");
  }
  
  console.log("下载转录结果：", transcriptionUrl);
  
  const https = require('https');
  const http = require('http');
  const url = require('url');
  
  const parsedUrl = url.parse(transcriptionUrl);
  
  return new Promise((resolve, reject) => {
    const client = parsedUrl.protocol === 'https:' ? https : http;
    
    const req = client.get(transcriptionUrl, (res) => {
      let data = '';
      
      res.on('data', (chunk) => {
        data += chunk;
      });
      
      res.on('end', () => {
        try {
          const result = JSON.parse(data);
          resolve(result);
        } catch (error) {
          console.error("解析转录结果失败：", error);
          reject(new Error(`解析转录结果失败: ${error.message}`));
        }
      });
    });
    
    req.on('error', (error) => {
      console.error("下载转录结果失败：", error);
      reject(new Error(`下载转录结果失败: ${error.message}`));
    });
  });
}
