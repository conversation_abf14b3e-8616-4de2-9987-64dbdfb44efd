<template>
  <view class="upload-container">
    <!-- 重新设计的标题区域 -->
    <view class="header-section">
      <view class="header-content">
        <view class="title-group">
          <text class="page-title">智能字幕制作</text>
          <text class="page-subtitle">支持本地上传和链接解析，一键生成专业字幕</text>
        </view>
        <view class="feature-tags">
          <text class="feature-tag">AI识别</text>
          <text class="feature-tag">多语言</text>
          <text class="feature-tag">无水印</text>
        </view>
      </view>
    </view>

    <!-- 主要内容区域 -->
    <view class="main-content">
      <!-- 登录提示 - 优先显示 -->
      <view v-if="!userInfo.isLogin" class="login-prompt-section">
        <view class="login-card">
          <view class="login-header">
            <view class="login-icon">🔐</view>
            <view class="login-info">
              <text class="login-title">登录后开始使用</text>
              <text class="login-desc">微信登录，享受完整服务体验</text>
            </view>
          </view>
          <button @click="goToLogin" class="login-btn">
            <text class="btn-text">立即登录</text>
          </button>
        </view>
      </view>

      <!-- 已登录时显示的功能区域 -->
      <template v-if="userInfo.isLogin">
        <!-- 功能模式选择 -->
        <view class="mode-selection" v-if="!isUploading && !uploadSuccess">
          <view class="mode-header">
            <text class="mode-title">选择处理方式</text>
            <text class="mode-desc">选择最适合您的视频来源方式</text>
          </view>
          
          <view class="mode-options">
            <view 
              class="mode-option" 
              :class="{ 'option-active': currentMode === 'upload' }"
              @click="switchMode('upload')"
            >
              <view class="option-icon upload-icon">📁</view>
              <view class="option-content">
                <text class="option-title">本地上传</text>
                <text class="option-desc">从相册或拍摄选择视频文件</text>
              </view>
              <view class="option-indicator">
                <text class="indicator-dot"></text>
              </view>
            </view>

            <view 
              class="mode-option" 
              :class="{ 'option-active': currentMode === 'link' }"
              @click="switchMode('link')"
            >
              <view class="option-icon link-icon">🔗</view>
              <view class="option-content">
                <text class="option-title">链接解析</text>
                <text class="option-desc">抖音、小红书视频链接解析</text>
              </view>
              <view class="option-indicator">
                <text class="indicator-dot"></text>
              </view>
            </view>
          </view>
        </view>

        <!-- 本地上传模式 -->
        <view v-if="currentMode === 'upload' && !isUploading && !uploadSuccess" class="upload-mode-section">
          <view class="upload-area" @click="selectVideo">
            <view class="upload-icon">🎬</view>
            <text class="upload-title">选择本地视频</text>
            <text class="upload-desc">{{ uploadDescText }}</text>
            <view class="upload-btn">
              <text class="btn-text">点击选择文件</text>
            </view>
          </view>
        </view>

        <!-- 链接解析模式 -->
        <view v-if="currentMode === 'link' && !isUploading && !uploadSuccess" class="link-mode-section">
          <VideoLinkParser 
            :disabled="isUploading"
            :userInfo="userInfo"
            @parsed="handleParsedVideo"
            @error="handleParseError"
          />
        </view>

        <!-- 上传/处理进度区域 -->
        <view v-if="isUploading" class="processing-section">
          <view class="processing-card">
            <view class="processing-header">
              <view class="processing-icon">
                <text class="processing-emoji">⚡</text>
              </view>
              <view class="processing-info">
                <text class="processing-title">{{ uploadStatus || '处理中...' }}</text>
                <text class="processing-desc">{{ getProcessingDesc() }}</text>
              </view>
            </view>

            <!-- 获取凭证阶段 -->
            <view v-if="!uploadProgress" class="loading-stage">
              <view class="loading-spinner"></view>
              <text class="loading-text">正在准备处理环境...</text>
            </view>

            <!-- 上传进度详情 -->
            <view v-if="uploadProgress > 0" class="upload-progress-details">
              <view class="progress-main">
                <text class="progress-percentage">{{ uploadProgress }}%</text>
                <text class="progress-size">{{ formatFileSize(uploadDetails.uploadedSize) }} / {{ formatFileSize(uploadDetails.totalSize) }}</text>
              </view>

              <!-- 进度条 -->
              <view class="progress-container">
                <view class="progress-bar">
                  <view class="progress-fill" :style="{ width: uploadProgress + '%' }">
                    <view class="progress-glow"></view>
                  </view>
                </view>
              </view>

              <!-- 上传速度和剩余时间 -->
              <view class="progress-stats">
                <text class="upload-speed">{{ formatSpeed(uploadDetails.uploadSpeed) }}</text>
                <text class="remaining-time" v-if="uploadDetails.remainingTime > 0">剩余 {{ formatTime(uploadDetails.remainingTime) }}</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 成功状态 -->
        <view v-if="uploadSuccess" class="success-section">
          <view class="success-card">
            <view class="success-animation">
              <text class="success-icon">🎉</text>
              <view class="success-rings">
                <view class="success-ring"></view>
                <view class="success-ring success-ring-delay"></view>
              </view>
            </view>
            <text class="success-title">处理成功！</text>
            <text class="success-desc">正在跳转到处理页面，请稍候...</text>
          </view>
        </view>

        <!-- 语言设置区域 - 独立显示 -->
        <view v-if="!isUploading && !uploadSuccess" class="language-section">
          <view class="language-card">
            <view class="language-header">
              <view class="language-icon">🌐</view>
              <view class="language-info">
                <text class="language-title">语言设置</text>
                <text class="language-desc">智能识别源语言，精准翻译目标语言</text>
              </view>
            </view>
            <LanguageSelector
              v-model:sourceLanguage="selectedSourceLanguage"
              v-model:targetLanguage="selectedTargetLanguage"
            />
          </view>
        </view>

        <!-- 功能特性展示 -->
        <view v-if="!isUploading && !uploadSuccess" class="features-section">
          <view class="features-header">
            <text class="features-title">核心功能</text>
            <text class="features-desc">专业级视频字幕处理能力</text>
          </view>
          
          <view class="features-grid">
            <view class="feature-card">
              <view class="feature-icon ai-icon">🤖</view>
              <view class="feature-content">
                <text class="feature-name">AI语音识别</text>
                <text class="feature-detail">支持{{ uploadConfig.supportedFormatsDesc }}</text>
              </view>
            </view>

            <view class="feature-card">
              <view class="feature-icon size-icon">📊</view>
              <view class="feature-content">
                <text class="feature-name">智能压缩</text>
                <text class="feature-detail">{{ fileSizeDescText }}</text>
              </view>
            </view>

            <view class="feature-card">
              <view class="feature-icon speed-icon">⚡</view>
              <view class="feature-content">
                <text class="feature-name">极速处理</text>
                <text class="feature-detail">1-5分钟完成字幕制作</text>
              </view>
            </view>

            <view class="feature-card">
              <view class="feature-icon quality-icon">✨</view>
              <view class="feature-content">
                <text class="feature-name">无水印下载</text>
                <text class="feature-detail">支持抖音、小红书链接</text>
              </view>
            </view>
          </view>
        </view>
      </template>
    </view>

    <!-- 底部安全区域 -->
    <view class="bottom-safe-area"></view>

    <!-- 错误提示组件 -->
    <ErrorToast
      :visible="errorState.visible"
      :type="errorState.type"
      :title="errorState.title"
      :message="errorState.message"
      :details="errorState.details"
      :suggestions="errorState.suggestions"
      :showRetry="errorState.showRetry"
      :showReport="errorState.showReport"
      @close="closeError"
      @retry="handleErrorRetry"
      @report="handleErrorReport"
    />
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, computed } from 'vue'
import { onShow } from "@dcloudio/uni-app";
import type { SelectedVideo } from '@/types/video'
import { checkLoginStatus, handleLoginCheck, type LoginCheckResult } from '@/utils/common'
import { getVideoUploadConfig, generateUploadDesc, generateFileSizeDesc, formatMaxFileSize, formatMaxDuration } from '@/config'
import LanguageSelector from '@/components/LanguageSelector.vue'
import VideoLinkParser from '@/components/VideoLinkParser.vue'
import ErrorToast from '@/components/ErrorToast.vue'
// 移除VOD SDK引用，改用OSS直传

// 获取配置
const uploadConfig = getVideoUploadConfig()

// 计算属性：动态生成配置相关的文本
const uploadDescText = computed(() => generateUploadDesc(uploadConfig))
const fileSizeDescText = computed(() => generateFileSizeDesc(uploadConfig))

// 新增：模式选择
const currentMode = ref<'upload' | 'link'>('upload')

// 响应式数据
const isUploading = ref(false)
const selectedVideo = ref<SelectedVideo | null>(null)
const uploadProgress = ref(0)
const uploadStatus = ref('')
const currentTaskId = ref('')

// 新增：详细上传进度信息
const uploadDetails = ref({
  uploadedSize: 0,
  totalSize: 0,
  uploadSpeed: 0,
  remainingTime: 0,
  startTime: 0
})

// 新增：上传成功状态
const uploadSuccess = ref(false)

// 语言选择
const selectedSourceLanguage = ref('auto')
const selectedTargetLanguage = ref('zh')

// 用户信息
const userInfo = ref({
  openid: '',
  isLogin: false
})

// 错误状态管理
const errorState = ref({
  visible: false,
  type: 'error' as 'error' | 'warning' | 'info',
  title: '',
  message: '',
  details: '',
  suggestions: [] as string[],
  showRetry: false,
  showReport: false,
  retryAction: null as (() => void) | null
})

// 显示错误提示
const showError = (config: {
  type?: 'error' | 'warning' | 'info'
  title?: string
  message: string
  details?: string
  suggestions?: string[]
  showRetry?: boolean
  showReport?: boolean
  retryAction?: () => void
}) => {
  errorState.value = {
    visible: true,
    type: config.type || 'error',
    title: config.title || '',
    message: config.message,
    details: config.details || '',
    suggestions: config.suggestions || [],
    showRetry: config.showRetry || false,
    showReport: config.showReport || false,
    retryAction: config.retryAction || null
  }
}

// 关闭错误提示
const closeError = () => {
  errorState.value.visible = false
}

// 处理错误重试
const handleErrorRetry = () => {
  if (errorState.value.retryAction) {
    errorState.value.retryAction()
  }
}

// 处理错误反馈
const handleErrorReport = (reportData: any) => {
  console.log('用户反馈错误信息:', reportData)
  
  // 这里可以发送错误报告到服务器
  uni.showToast({
    title: '反馈已提交，感谢您的帮助',
    icon: 'success',
    duration: 2000
  })
}

// 切换模式
const switchMode = (mode: 'upload' | 'link') => {
  currentMode.value = mode
  // 重置状态
  selectedVideo.value = null
  resetUploadState()
}

// 获取处理描述
const getProcessingDesc = () => {
  if (currentMode.value === 'link') {
    return '正在从平台下载视频并处理...'
  }
  return '正在上传视频文件并处理...'
}

// 处理解析到的视频
const handleParsedVideo = async (result: any) => {
  console.log('接收到解析结果:', result)
  
  try {
    isUploading.value = true
    uploadStatus.value = '视频解析成功！'
    
    // 由于parse-video-link云函数已经创建了任务并开始处理，
    // 这里直接获取taskId并跳转到处理页面
    const taskId = result.task?.taskId
    
    if (!taskId) {
      throw new Error('未获取到任务ID，无法继续处理')
    }
    
    currentTaskId.value = taskId
    
    // 显示成功状态
    uploadSuccess.value = true
    uploadStatus.value = '解析成功！正在跳转...'
    
    console.log('视频解析完成，taskId:', taskId)
    
    // 跳转到处理页面
    setTimeout(() => {
      uni.navigateTo({
        url: `/pages/process/process?taskId=${taskId}&fromLink=true`,
        success: () => {
          resetUploadState()
        },
        fail: (error) => {
          console.error('页面跳转失败:', error)
          uni.showToast({
            title: '页面跳转失败',
            icon: 'none'
          })
          resetUploadState()
        }
      })
    }, 1000)
    
  } catch (error: any) {
    console.error('处理解析视频失败:', error)
    uni.showToast({
      title: error.message || '处理失败',
      icon: 'none'
    })
    resetUploadState()
  }
}

// 处理解析错误
const handleParseError = (error: string) => {
  console.error('视频解析错误:', error)
  
  // 根据错误类型提供不同的建议
  let suggestions: string[] = []
  let details = ''
  
  if (error.includes('不支持的链接格式')) {
    suggestions = [
      '确认链接来自抖音或小红书官方app',
      '检查链接是否完整且未过期',
      '尝试复制最新的分享链接'
    ]
    details = '当前仅支持抖音(douyin.com)和小红书(xiaohongshu.com)的视频分享链接'
  } else if (error.includes('网络')) {
    suggestions = [
      '检查网络连接是否正常',
      '尝试切换到更稳定的网络环境',
      '稍后重试'
    ]
    details = '网络连接异常，无法访问视频平台服务器'
  } else if (error.includes('解析失败')) {
    suggestions = [
      '确认视频链接有效且可正常访问',
      '检查视频是否已被删除或设为私密',
      '尝试使用其他视频链接'
    ]
    details = '视频解析过程中遇到问题，可能是视频不可访问或链接格式异常'
  } else {
    suggestions = [
      '检查网络连接',
      '确认链接格式正确',
      '稍后重试或尝试其他视频'
    ]
    details = error
  }
  
  showError({
    type: 'error',
    title: '视频解析失败',
    message: error,
    details: details,
    suggestions: suggestions,
    showRetry: true,
    showReport: true,
    retryAction: () => {
      // 重试时清空当前状态，让用户重新尝试
      resetUploadState()
    }
  })
}

// 页面加载时获取用户信息
onMounted(async () => {
  await loadUserInfo()
})

// 页面显示时检查登录状态
onShow(async () => {
  console.log('上传页面显示，检查登录状态')
  await checkAndUpdateLoginStatus()
})

// 加载用户信息（保持原有逻辑，用于页面初始化）
const loadUserInfo = async () => {
  try {
    // 从本地存储获取用户信息
    const savedUserInfo = uni.getStorageSync('userInfo')
    if (savedUserInfo && savedUserInfo.openid) {
      userInfo.value.openid = savedUserInfo.openid
      userInfo.value.isLogin = true
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 检查并更新登录状态（用于onShow生命周期）
const checkAndUpdateLoginStatus = async () => {
  try {
    const loginCheck: LoginCheckResult = await checkLoginStatus()

    // 更新本地登录状态
    userInfo.value.isLogin = loginCheck.isLogin
    userInfo.value.openid = loginCheck.openid || ''

    // 如果登录状态发生变化，处理相应逻辑
    if (!loginCheck.isLogin) {
      console.log('用户未登录或登录已过期:', loginCheck.reason)

      // 如果用户之前是登录状态但现在检查失败，显示提示
      if (loginCheck.needRelogin) {
        handleLoginCheck(loginCheck, {
          showToast: true,
          autoRedirect: false,
          customMessage: '登录已过期，请重新登录后使用上传功能'
        })
      }

      // 重置上传状态，防止在未登录状态下继续操作
      if (isUploading.value) {
        resetUploadState()
      }
    } else {
      console.log('用户登录状态正常，openid:', loginCheck.openid)
    }
  } catch (error) {
    console.error('检查登录状态失败:', error)
    // 发生错误时，保守处理，设置为未登录状态
    userInfo.value.isLogin = false
    userInfo.value.openid = ''
  }
}

// 跳转到登录页面
const goToLogin = () => {
  uni.switchTab({
    url: '/pages/profile/profile'
  })
}

// 格式化文件大小
const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 B'
  const k = 1024
  const sizes = ['B', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 格式化上传速度
const formatSpeed = (bytesPerSecond: number): string => {
  if (bytesPerSecond === 0) return '0 KB/s'
  const k = 1024
  const sizes = ['B/s', 'KB/s', 'MB/s']
  const i = Math.floor(Math.log(bytesPerSecond) / Math.log(k))
  return parseFloat((bytesPerSecond / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
}

// 格式化剩余时间
const formatTime = (seconds: number): string => {
  if (seconds < 60) return `${Math.round(seconds)}秒`
  const minutes = Math.floor(seconds / 60)
  const remainingSeconds = Math.round(seconds % 60)
  return `${minutes}分${remainingSeconds}秒`
}

// 重置上传状态
const resetUploadState = () => {
  isUploading.value = false
  uploadSuccess.value = false
  uploadProgress.value = 0
  uploadStatus.value = ''
  uploadDetails.value = {
    uploadedSize: 0,
    totalSize: 0,
    uploadSpeed: 0,
    remainingTime: 0,
    startTime: 0
  }
}



// 选择视频文件
const selectVideo = async () => {
  // 检查用户是否已登录
  if (!userInfo.value.isLogin || !userInfo.value.openid) {
    showError({
      type: 'warning',
      title: '需要登录',
      message: '请先登录后再使用上传功能',
      suggestions: [
        '点击右下角"个人中心"进行微信登录',
        '登录后即可享受完整服务'
      ],
      showRetry: false,
      showReport: false
    })
    return
  }

  try {
    // 使用uni.chooseVideo选择视频，使用配置的时长限制
    const chooseResult = await uni.chooseVideo({
      maxDuration: 60,
      sourceType: ['album', 'camera'],
      compressed: false,
      camera: 'back' 
    })

    console.log('选择视频结果：', chooseResult)

    const { tempFilePath, duration, size, name } = chooseResult

    // 检查视频时长
    if (duration > uploadConfig.maxDuration) {
      showError({
        type: 'warning',
        title: '视频时长超限',
        message: `视频时长不能超过${formatMaxDuration(uploadConfig)}`,
        details: `当前视频时长：${Math.floor(duration)}秒，最大允许：${uploadConfig.maxDuration}秒`,
        suggestions: [
          '使用视频编辑工具裁剪视频',
          '选择时长更短的视频片段',
          '分段处理长视频'
        ],
        showRetry: true,
        showReport: false,
        retryAction: selectVideo
      })
      return
    }

    // 检查文件大小
    if (size > uploadConfig.maxFileSize) {
      showError({
        type: 'warning',
        title: '文件大小超限',
        message: `文件大小不能超过${formatMaxFileSize(uploadConfig)}`,
        details: `当前文件大小：${Math.floor(size / 1024 / 1024)}MB，最大允许：${Math.floor(uploadConfig.maxFileSize / 1024 / 1024)}MB`,
        suggestions: [
          '使用视频压缩工具减小文件大小',
          '选择分辨率较低的视频',
          '尝试其他格式的视频文件'
        ],
        showRetry: true,
        showReport: false,
        retryAction: selectVideo
      })
      return
    }

    selectedVideo.value = {
      tempFilePath,
      duration,
      size,
      name: name || `video_${Date.now()}.mp4`
    }

    // 获取上传凭证
    await getUploadAuth()

  } catch (error: any) {
    console.error('选择视频失败：', error)
    
    let errorMessage = '选择视频失败'
    let suggestions = ['重新尝试选择视频', '检查相册权限设置']
    
    if (error.errMsg) {
      if (error.errMsg.includes('cancel')) {
        return // 用户取消，不显示错误
      } else if (error.errMsg.includes('permission')) {
        errorMessage = '没有访问相册的权限'
        suggestions = [
          '前往手机设置开启相册访问权限',
          '重新启动应用后再试'
        ]
      } else if (error.errMsg.includes('not support')) {
        errorMessage = '当前环境不支持选择视频'
        suggestions = [
          '更新应用到最新版本',
          '尝试使用其他设备'
        ]
      }
    }
    
    showError({
      type: 'error',
      title: '选择视频失败',
      message: errorMessage,
      details: error.errMsg || error.message || '未知错误',
      suggestions: suggestions,
      showRetry: true,
      showReport: true,
      retryAction: selectVideo
    })
  }
}

// 获取上传凭证
const getUploadAuth = async () => {
  if (!selectedVideo.value) {
    return
  }

  // 检查用户是否已登录
  if (!userInfo.value.isLogin || !userInfo.value.openid) {
    uni.showToast({
      title: '请先登录',
      icon: 'none'
    })
    // 跳转到登录页面
    uni.switchTab({
      url: '/pages/profile/profile'
    })
    return
  }

  try {
    isUploading.value = true
    uploadStatus.value = '正在获取上传策略...'

    // 调用云函数获取OSS上传策略
    console.log('上传参数:', {
      fileName: selectedVideo.value.name,
      fileSize: selectedVideo.value.size,
      duration: selectedVideo.value.duration,
      sourceLanguage: selectedSourceLanguage.value,
      targetLanguage: selectedTargetLanguage.value
    })

    const result = await uniCloud.callFunction({
      name: 'get-oss-upload-policy',
      data: {
        fileName: selectedVideo.value.name,
        fileSize: selectedVideo.value.size,
        duration: selectedVideo.value.duration,
        openid: userInfo.value.openid,
        sourceLanguage: selectedSourceLanguage.value,
        targetLanguage: selectedTargetLanguage.value
      }
    })

    console.log('获取OSS上传策略结果：', result)

    if (result.result.code === 0) {
      const { taskId, accessKeyId, host, policy, signature, key, fullUrl, fileInfo } = result.result.data

      // 开始OSS直传
      await startOSSUpload({
        taskId,
        host,
        accessKeyId,
        policy,
        signature,
        key,
        fullUrl,
        fileInfo
      })

    } else {
      uni.hideLoading()
      uni.showToast({
        title: result.result.message || '获取上传策略失败',
        icon: 'none'
      })
    }

  } catch (error) {
    console.error('获取上传凭证失败：', error)
    uni.hideLoading()
    uni.showToast({
      title: '获取凭证失败',
      icon: 'none'
    })
  } finally {
    isUploading.value = false
  }
}

// 开始OSS直传
const startOSSUpload = async (uploadInfo: {
  taskId: string
  host: string
  accessKeyId: string
  policy: string
  signature: string
  key: string
  fullUrl: string
  fileInfo: {
    fileName: string
    fileSize: number
    duration: number
    userId: string
  }
}) => {
  try {
    uploadStatus.value = '正在上传视频...'
    uploadProgress.value = 0

    console.log('开始OSS直传:', uploadInfo)

    // 使用 uni.uploadFile 直传到OSS
    const uploadTask = uni.uploadFile({
      url: uploadInfo.host, // OSS的域名
      filePath: selectedVideo.value!.tempFilePath,
      name: 'file', // 这个name必须是'file'
      formData: {
        'key': uploadInfo.key,
        'policy': uploadInfo.policy,
        'OSSAccessKeyId': uploadInfo.accessKeyId,
        'signature': uploadInfo.signature,
        'success_action_status': '200' // 让OSS返回200状态码
      },
      success: async (uploadRes) => {
        console.log('OSS上传响应:', uploadRes)

        if (uploadRes.statusCode === 204 || uploadRes.statusCode === 200) {
          console.log('上传成功!')
          uploadProgress.value = 100

          // 显示上传成功状态
          uploadSuccess.value = true
          uploadStatus.value = '上传成功！'

          try {
            const taskId = uploadInfo.taskId
            console.log('使用已创建的taskId:', taskId)
            currentTaskId.value = taskId

            // 更新任务状态为extracting_audio，并保存OSS URL
            const updateResult = await uniCloud.callFunction({
              name: 'update-task-status',
              data: {
                taskId: taskId,
                status: 'extracting_audio',
                ossUrl: uploadInfo.fullUrl,
                openid: userInfo.value.openid
              }
            })

            console.log('更新任务状态结果:', updateResult)

            if (updateResult.result.code === 200) {
              // 启动音频提取任务（异步，不等待结果）
              uniCloud.callFunction({
                name: 'process-video-task',
                data: {
                  taskId: taskId,
                  ossUrl: uploadInfo.fullUrl,
                  action: 'extract_audio'
                }
              }).then(processResult => {
                console.log('调用音频提取任务结果:', processResult)
              }).catch(error => {
                console.error('启动处理任务失败:', error)
              })

              // 立即跳转到处理页面，不等待处理任务启动
              setTimeout(() => {
                uni.navigateTo({
                  url: `/pages/process/process?taskId=${taskId}&ossUrl=${encodeURIComponent(uploadInfo.fullUrl)}`,
                  success: () => {
                    // 跳转成功后重置状态
                    resetUploadState()
                  },
                  fail: (error) => {
                    console.error('页面跳转失败:', error)
                    uni.showToast({
                      title: '页面跳转失败',
                      icon: 'none'
                    })
                    resetUploadState()
                  }
                })
              }, 1000) // 减少等待时间到1秒，让用户看到成功动画
            } else {
              throw new Error(updateResult.result.message || '更新任务状态失败')
            }
          } catch (error) {
            console.error('处理失败:', error)
            uploadSuccess.value = false
            uploadStatus.value = '处理失败'
            uni.showToast({
              title: '处理失败: ' + (error instanceof Error ? error.message : '未知错误'),
              icon: 'none'
            })
            // 3秒后重置状态，允许用户重新上传
            setTimeout(() => {
              resetUploadState()
            }, 3000)
          }
        } else {
          console.error('上传失败，状态码:', uploadRes.statusCode, uploadRes.data)
          uploadStatus.value = '上传失败'
          uni.showToast({
            title: `上传失败: ${uploadRes.statusCode}`,
            icon: 'none'
          })
          // 3秒后重置状态
          setTimeout(() => {
            resetUploadState()
          }, 3000)
        }
      },
      fail: (err) => {
        console.error('上传API调用失败:', err)
        uploadStatus.value = '上传失败'
        uni.showToast({
          title: '上传请求失败',
          icon: 'none'
        })
        // 3秒后重置状态
        setTimeout(() => {
          resetUploadState()
        }, 3000)
      },
      complete: () => {
        // 只有在非成功状态下才重置 isUploading
        if (!uploadSuccess.value) {
          isUploading.value = false
        }
      }
    })

    // 监听上传进度
    uploadTask.onProgressUpdate((progressRes) => {
      const currentTime = Date.now()
      const progress = progressRes.progress
      const totalBytes = progressRes.totalBytesSent
      const uploadedBytes = progressRes.totalBytesExpectedToSend

      // 更新基本进度
      uploadProgress.value = progress
      uploadStatus.value = `上传中... ${progress}%`

      // 计算详细信息
      if (uploadDetails.value.startTime === 0) {
        uploadDetails.value.startTime = currentTime
        uploadDetails.value.totalSize = uploadedBytes
      }

      uploadDetails.value.uploadedSize = totalBytes

      // 计算上传速度（字节/秒）
      const elapsedTime = (currentTime - uploadDetails.value.startTime) / 1000
      if (elapsedTime > 0) {
        uploadDetails.value.uploadSpeed = totalBytes / elapsedTime

        // 计算剩余时间
        const remainingBytes = uploadedBytes - totalBytes
        if (uploadDetails.value.uploadSpeed > 0 && remainingBytes > 0) {
          uploadDetails.value.remainingTime = remainingBytes / uploadDetails.value.uploadSpeed
        }
      }

      console.log('上传进度详情:', {
        progress: progress + '%',
        uploaded: formatFileSize(totalBytes),
        total: formatFileSize(uploadedBytes),
        speed: formatSpeed(uploadDetails.value.uploadSpeed),
        remaining: formatTime(uploadDetails.value.remainingTime)
      })
    })

  } catch (error) {
    console.error('上传过程出错:', error)
    uploadStatus.value = '上传出错'

    uni.showToast({
      title: '上传出错',
      icon: 'none'
    })

    // 3秒后重置状态
    setTimeout(() => {
      resetUploadState()
    }, 3000)
  }
}

</script>

<style scoped>
/* ==================== 基础布局 ==================== */
.upload-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  position: relative;
  padding-bottom: 120rpx;
}

.upload-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 20% 80%, rgba(99, 102, 241, 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(99, 102, 241, 0.06) 0%, transparent 50%);
  pointer-events: none;
}

/* ==================== 头部区域 ==================== */
.header-section {
  padding: 60rpx 32rpx;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  border-radius: 0 0 48rpx 48rpx;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.header-section::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
  opacity: 0.3;
}

.header-content {
  text-align: center;
  position: relative;
  z-index: 1;
}

.title-group {
  margin-bottom: 32rpx;
}

.page-title {
  display: block;
  font-size: 56rpx;
  font-weight: 700;
  color: white;
  margin-bottom: 16rpx;
  line-height: 1.2;
}

.page-subtitle {
  display: block;
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.6;
  font-weight: 400;
}

.feature-tags {
  display: flex;
  justify-content: center;
  gap: 16rpx;
  flex-wrap: wrap;
}

.feature-tag {
  padding: 8rpx 20rpx;
  background: rgba(255, 255, 255, 0.2);
  border: 1rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 20rpx;
  color: white;
  font-size: 22rpx;
  font-weight: 600;
  backdrop-filter: blur(10rpx);
}

/* ==================== 主要内容区域 ==================== */
.main-content {
  flex: 1;
  padding: 0 32rpx 32rpx;
  position: relative;
  z-index: 2;
}

/* ==================== 登录提示区域 ==================== */
.login-prompt-section {
  margin-bottom: 32rpx;
}

.login-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid #fef3c7;
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
  position: relative;
  overflow: hidden;
}

.login-card::before {
  content: "";
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(245, 158, 11, 0.05) 0%, transparent 70%);
  pointer-events: none;
}

.login-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
  margin-bottom: 32rpx;
  position: relative;
  z-index: 1;
}

.login-icon {
  font-size: 48rpx;
  width: 64rpx;
  height: 64rpx;
  line-height: 64rpx;
  text-align: center;
  border-radius: 16rpx;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

.login-info {
  flex: 1;
}

.login-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #92400e;
  margin-bottom: 8rpx;
}

.login-desc {
  display: block;
  font-size: 26rpx;
  color: #a16207;
  line-height: 1.5;
}

.login-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(245, 158, 11, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  z-index: 1;
}

.login-btn:active {
  transform: translateY(-2rpx);
  box-shadow: 0 12rpx 32rpx rgba(245, 158, 11, 0.4);
}

/* ==================== 模式选择区域 ==================== */
.mode-selection {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid transparent;
}

.mode-header {
  text-align: center;
  margin-bottom: 32rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f3f4f6;
}

.mode-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.mode-desc {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
}

.mode-options {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.mode-option {
  display: flex;
  align-items: center;
  padding: 24rpx;
  border: 2rpx solid #e5e7eb;
  border-radius: 16rpx;
  background: #f9fafb;
  transition: all 0.3s ease;
  cursor: pointer;
}

.mode-option:active {
  transform: translateY(-2rpx);
}

.mode-option.option-active {
  border-color: #6366f1;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  box-shadow: 0 4rpx 16rpx rgba(99, 102, 241, 0.2);
}

.option-icon {
  font-size: 40rpx;
  width: 56rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  border-radius: 12rpx;
  margin-right: 16rpx;
}

.upload-icon {
  background: linear-gradient(135deg, #ddd6fe 0%, #c4b5fd 100%);
}

.link-icon {
  background: linear-gradient(135deg, #bfdbfe 0%, #93c5fd 100%);
}

.option-content {
  flex: 1;
}

.option-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.option-desc {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

.option-indicator {
  width: 20rpx;
  height: 20rpx;
  border-radius: 50%;
  border: 2rpx solid #d1d5db;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.option-active .option-indicator {
  border-color: #6366f1;
  background: #6366f1;
}

.indicator-dot {
  width: 8rpx;
  height: 8rpx;
  border-radius: 50%;
  background: white;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.option-active .indicator-dot {
  opacity: 1;
}

/* ==================== 上传模式区域 ==================== */
.upload-mode-section {
  margin-bottom: 24rpx;
}

.upload-area {
  background: white;
  border-radius: 24rpx;
  padding: 64rpx 32rpx;
  text-align: center;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 2rpx dashed #d1d5db;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-area:active {
  transform: translateY(-4rpx);
  box-shadow: 0 12rpx 48rpx rgba(0, 0, 0, 0.12);
  border-color: #6366f1;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
}

.upload-icon {
  font-size: 80rpx;
  margin-bottom: 24rpx;
  display: block;
}

.upload-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 16rpx;
}

.upload-desc {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
  margin-bottom: 32rpx;
}

.upload-btn {
  display: inline-block;
  padding: 16rpx 40rpx;
  background: linear-gradient(135deg, #6366f1 0%, #4f46e5 100%);
  color: white;
  border-radius: 16rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

/* ==================== 链接模式区域 ==================== */
.link-mode-section {
  margin-bottom: 24rpx;
}

/* ==================== 处理进度区域 ==================== */
.processing-section {
  margin-bottom: 24rpx;
}

.processing-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid #fbbf24;
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.processing-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

.processing-icon {
  width: 56rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #fbbf24 0%, #f59e0b 100%);
}

.processing-emoji {
  font-size: 32rpx;
  animation: pulse 2s infinite;
}

.processing-info {
  flex: 1;
}

.processing-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: #92400e;
  margin-bottom: 8rpx;
}

.processing-desc {
  display: block;
  font-size: 24rpx;
  color: #a16207;
  line-height: 1.4;
}

.loading-stage {
  text-align: center;
  padding: 40rpx 0;
}

.loading-spinner {
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid rgba(251, 191, 36, 0.3);
  border-left-color: #fbbf24;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 16rpx;
}

.loading-text {
  font-size: 26rpx;
  color: #a16207;
}

/* ==================== 上传进度详情 ==================== */
.upload-progress-details {
  margin-top: 24rpx;
}

.progress-main {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.progress-percentage {
  font-size: 32rpx;
  font-weight: 700;
  color: #f59e0b;
}

.progress-size {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

.progress-container {
  width: 100%;
  margin-bottom: 16rpx;
}

.progress-bar {
  width: 100%;
  height: 16rpx;
  background-color: rgba(245, 158, 11, 0.2);
  border-radius: 8rpx;
  overflow: hidden;
  position: relative;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
  border-radius: 8rpx;
  transition: width 0.3s ease;
  position: relative;
  overflow: hidden;
}

.progress-glow {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  animation: progress-shimmer 2s infinite;
}

.progress-stats {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.upload-speed {
  font-size: 24rpx;
  color: #10b981;
  font-weight: 600;
}

.remaining-time {
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
}

/* ==================== 成功状态区域 ==================== */
.success-section {
  margin-bottom: 24rpx;
}

.success-card {
  background: white;
  border-radius: 24rpx;
  padding: 48rpx 32rpx;
  text-align: center;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid #10b981;
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.success-animation {
  position: relative;
  margin-bottom: 24rpx;
}

.success-icon {
  font-size: 80rpx;
  animation: success-bounce 0.6s ease-out;
}

.success-rings {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  pointer-events: none;
}

.success-ring {
  position: absolute;
  width: 80rpx;
  height: 80rpx;
  border: 3rpx solid rgba(16, 185, 129, 0.6);
  border-radius: 50%;
  animation: success-expand 1.5s ease-out infinite;
}

.success-ring-delay {
  animation-delay: 0.5s;
}

.success-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #10b981;
  margin-bottom: 12rpx;
}

.success-desc {
  display: block;
  font-size: 26rpx;
  color: #047857;
  line-height: 1.5;
}

/* ==================== 语言设置区域 ==================== */
.language-section {
  margin-bottom: 24rpx;
}

.language-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
  border: 2rpx solid transparent;
}

.language-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f3f4f6;
}

.language-icon {
  font-size: 36rpx;
  width: 48rpx;
  height: 48rpx;
  line-height: 48rpx;
  text-align: center;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
}

.language-info {
  flex: 1;
}

.language-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.language-desc {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

/* ==================== 功能特性区域 ==================== */
.features-section {
  margin-bottom: 32rpx;
}

.features-header {
  text-align: center;
  margin-bottom: 32rpx;
}

.features-title {
  display: block;
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
  margin-bottom: 12rpx;
}

.features-desc {
  display: block;
  font-size: 26rpx;
  color: #6b7280;
  line-height: 1.5;
}

.features-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16rpx;
}

.feature-card {
  background: white;
  border-radius: 20rpx;
  padding: 24rpx 20rpx;
  text-align: center;
  border: 2rpx solid transparent;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
  transition: all 0.3s ease;
}

.feature-card:active {
  transform: translateY(-2rpx);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.08);
  border-color: #e0e7ff;
}

.feature-icon {
  font-size: 40rpx;
  margin-bottom: 16rpx;
  display: block;
  height: 56rpx;
  line-height: 56rpx;
}

.feature-content {
  text-align: center;
}

.feature-name {
  display: block;
  font-size: 26rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8rpx;
}

.feature-detail {
  display: block;
  font-size: 22rpx;
  color: #6b7280;
  line-height: 1.4;
}

.bottom-safe-area {
  height: 40rpx;
}

/* ==================== 动画效果 ==================== */
@keyframes spin {
  to { transform: rotate(360deg); }
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

@keyframes progress-shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

@keyframes success-bounce {
  0% { transform: scale(0.3); opacity: 0; }
  50% { transform: scale(1.1); }
  100% { transform: scale(1); opacity: 1; }
}

@keyframes success-expand {
  0% {
    width: 80rpx;
    height: 80rpx;
    opacity: 1;
  }
  100% {
    width: 120rpx;
    height: 120rpx;
    opacity: 0;
  }
}



/* ==================== 响应式设计 ==================== */
@media (max-width: 750rpx) {
  .main-content {
    padding: 0 24rpx 24rpx;
  }

  .features-grid {
    grid-template-columns: 1fr;
    gap: 12rpx;
  }

  .mode-options {
    gap: 12rpx;
  }

  .mode-option {
    padding: 20rpx;
  }
}
</style>
