<template>
  <view class="profile-container">
    <!-- 用户信息头部 - 重新设计 -->
    <view class="user-header">
      <view class="user-content">
        <view class="avatar-section">
          <view class="avatar-wrapper">
            <image
              class="avatar"
              :src="userInfo.avatar || '/static/default-avatar.svg'"
              mode="aspectFill"
            />
            <view v-if="userInfo.isLogin" class="status-indicator online"></view>
            <view v-else class="status-indicator offline"></view>
          </view>
          <view class="user-info">
            <text class="username">{{ userInfo.nickname || '未登录用户' }}</text>
            <text class="user-id">{{ getUserIdText() }}</text>
            <view v-if="userInfo.isLogin" class="user-badge">
              <text class="badge-text">已认证</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 微信登录按钮 - 居中显示 -->
      <view v-if="!userInfo.isLogin" class="login-section">
        <button
          @click="handleModernWechatLogin"
          class="btn btn-primary login-btn-centered"
        >
          <text class="btn-icon">🔐</text>
          <text>微信登录</text>
        </button>
        <text class="login-tip">登录后享受更多功能</text>
      </view>
    </view>

    <!-- 统计信息卡片 - 响应式重新设计 -->
    <view class="stats-section">
      <view class="section-header">
        <text class="section-title">使用统计</text>
        <text class="section-subtitle">{{ userInfo.isLogin ? '您的使用数据概览' : '登录后查看详细统计' }}</text>
      </view>

      <!-- 已登录状态显示详细统计 -->
      <view v-if="userInfo.isLogin" class="stats-container">
        <!-- 加载状态 -->
        <view v-if="isLoadingStats" class="stats-loading">
          <text class="loading-text">加载统计数据中...</text>
        </view>

        <!-- 主要统计 -->
        <view v-else class="primary-stats">
          <view class="primary-stat-item">
            <view class="stat-icon-wrapper primary">
              <text class="stat-icon">📊</text>
            </view>
            <view class="stat-content">
              <text class="stat-number">{{ stats.totalTasks }}</text>
              <text class="stat-label">总任务数</text>
            </view>
          </view>
          <view class="primary-stat-item">
            <view class="stat-icon-wrapper success">
              <text class="stat-icon">✅</text>
            </view>
            <view class="stat-content">
              <text class="stat-number">{{ stats.completedTasks }}</text>
              <text class="stat-label">已完成</text>
            </view>
          </view>
        </view>

        <!-- 次要统计 -->
        <view v-if="!isLoadingStats" class="secondary-stats">
          <view class="secondary-stat-item">
            <view class="stat-icon-wrapper info">
              <text class="stat-icon">⏱️</text>
            </view>
            <view class="stat-content">
              <text class="stat-number">{{ formatDuration(stats.totalDuration) }}</text>
              <text class="stat-label">总时长</text>
            </view>
          </view>
          <view class="secondary-stat-item">
            <view class="stat-icon-wrapper warning">
              <text class="stat-icon">📁</text>
            </view>
            <view class="stat-content">
              <text class="stat-number">{{ formatFileSize(stats.totalSize) }}</text>
              <text class="stat-label">处理文件</text>
            </view>
          </view>
        </view>

        <!-- 最近活动 -->
        <view v-if="!isLoadingStats && recentActivities.length > 0" class="recent-activities">
          <view class="activity-header">
            <text class="activity-title">最近活动</text>
          </view>
          <view class="activity-list">
            <view
              v-for="activity in recentActivities.slice(0, 3)"
              :key="activity.id"
              class="activity-item"
            >
              <view class="activity-icon">
                <text>{{ getActivityIcon(activity.type) }}</text>
              </view>
              <view class="activity-content">
                <text class="activity-text">{{ activity.description }}</text>
                <text class="activity-time">{{ formatTime(activity.createdAt) }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 未登录状态显示提示信息 -->
      <view v-else class="login-prompt-container">
        <view class="login-prompt-card">
          <view class="prompt-icon-wrapper">
            <text class="prompt-icon">🔐</text>
          </view>
          <view class="prompt-content">
            <text class="prompt-title">登录后查看统计</text>
            <text class="prompt-desc">登录微信账号后，您可以查看详细的使用统计数据</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 功能菜单 - 只在登录后显示 -->
    <view v-if="userInfo.isLogin" class="menu-section">
      <view class="section-header">
        <text class="section-title">功能菜单</text>
        <text class="section-subtitle">快速访问常用功能</text>
      </view>

      <view class="menu-container">
        <view class="menu-item" @click="navigateTo('/pages/history/history')">
          <view class="menu-icon-wrapper history">
            <text class="menu-icon">📋</text>
          </view>
          <view class="menu-content">
            <text class="menu-text">历史记录</text>
            <text class="menu-desc">查看处理历史</text>
          </view>
          <view class="menu-arrow">
            <text class="arrow-icon">→</text>
          </view>
        </view>

        <view class="menu-item" @click="showSettings">
          <view class="menu-icon-wrapper settings">
            <text class="menu-icon">⚙️</text>
          </view>
          <view class="menu-content">
            <text class="menu-text">设置</text>
            <text class="menu-desc">个性化设置</text>
          </view>
          <view class="menu-arrow">
            <text class="arrow-icon">→</text>
          </view>
        </view>

        <view class="menu-item" @click="showAbout">
          <view class="menu-icon-wrapper about">
            <text class="menu-icon">ℹ️</text>
          </view>
          <view class="menu-content">
            <text class="menu-text">关于我们</text>
            <text class="menu-desc">了解更多信息</text>
          </view>
          <view class="menu-arrow">
            <text class="arrow-icon">→</text>
          </view>
        </view>

        <view class="menu-item" @click="showHelp">
          <view class="menu-icon-wrapper help">
            <text class="menu-icon">❓</text>
          </view>
          <view class="menu-content">
            <text class="menu-text">帮助与反馈</text>
            <text class="menu-desc">获取帮助支持</text>
          </view>
          <view class="menu-arrow">
            <text class="arrow-icon">→</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 版本信息 - 重新设计 -->
    <view class="footer-section">
      <view class="version-info">
        <text class="version-text">智能字幕胶囊 v1.0.0</text>
        <text class="copyright-text">© 2024 All Rights Reserved</text>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { getDeviceId } from '@/utils/common'
import { getWechatOpenid } from '@/utils/api'

// 用户信息
const userInfo = ref({
  nickname: '',
  avatar: '',
  deviceId: '',
  openid: '',
  isLogin: false
})

// 统计信息
const stats = ref({
  totalTasks: 0,
  completedTasks: 0,
  failedTasks: 0,
  cancelledTasks: 0,
  totalDuration: 0,
  totalSize: 0,
  averageProcessTime: 0
})

// 最近活动
const recentActivities = ref([])

// 加载状态
const isLoadingStats = ref(false)

// 页面加载时获取用户信息和统计数据
onMounted(async () => {
  await loadUserInfo()
  if (userInfo.value.isLogin) {
    await loadStats()
    await loadRecentActivities()
  }
})

// 加载用户信息
const loadUserInfo = async () => {
  try {
    const deviceId = getDeviceId()
    userInfo.value.deviceId = deviceId

    // 尝试从本地存储获取用户信息
    const savedUserInfo = uni.getStorageSync('userInfo')
    if (savedUserInfo) {
      userInfo.value.nickname = savedUserInfo.nickname
      userInfo.value.avatar = savedUserInfo.avatar
      userInfo.value.openid = savedUserInfo.openid || ''
      userInfo.value.isLogin = true
    } else {
      userInfo.value.nickname = `用户${deviceId.slice(-6)}`
    }
  } catch (error) {
    console.error('加载用户信息失败:', error)
  }
}

// 获取用户ID显示文本
const getUserIdText = (): string => {
  if (userInfo.value.isLogin) {
    return `ID: ${userInfo.value.deviceId?.slice(-8) || 'N/A'}`
  }
  return '点击登录获取更多功能'
}

// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (!size) return '0B'
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB'
  return (size / (1024 * 1024 * 1024)).toFixed(1) + 'GB'
}

// 加载统计数据
const loadStats = async () => {
  if (!userInfo.value.isLogin || !userInfo.value.openid) {
    return
  }

  try {
    isLoadingStats.value = true

    // 调用云函数获取用户统计数据
    const result = await uniCloud.callFunction({
      name: 'get-user-stats',
      data: {
        openid: userInfo.value.openid
      }
    })

    console.log('用户统计数据:', result)

    if (result.result.code === 200) {
      const data = result.result.data
      stats.value = {
        totalTasks: data.totalTasks || 0,
        completedTasks: data.completedTasks || 0,
        failedTasks: data.failedTasks || 0,
        cancelledTasks: data.cancelledTasks || 0,
        totalDuration: data.totalDuration || 0,
        totalSize: data.totalSize || 0,
        averageProcessTime: data.averageProcessTime || 0
      }
    } else {
      console.error('获取统计数据失败:', result.result.message)
    }

  } catch (error) {
    console.error('加载统计数据失败:', error)
  } finally {
    isLoadingStats.value = false
  }
}

// 加载最近活动
const loadRecentActivities = async () => {
  if (!userInfo.value.isLogin || !userInfo.value.openid) {
    return
  }

  try {
    const result = await uniCloud.callFunction({
      name: 'get-user-activities',
      data: {
        openid: userInfo.value.openid,
        limit: 5
      }
    })

    if (result.result.code === 200) {
      recentActivities.value = result.result.data || []
    }

  } catch (error) {
    console.error('加载最近活动失败:', error)
  }
}

// 格式化时长
const formatDuration = (seconds: number): string => {
  if (!seconds) return '0分钟'

  const hours = Math.floor(seconds / 3600)
  const minutes = Math.floor((seconds % 3600) / 60)

  if (hours > 0) {
    return `${hours}小时${minutes}分钟`
  } else {
    return `${minutes}分钟`
  }
}

// 获取活动图标
const getActivityIcon = (type: string): string => {
  switch (type) {
    case 'task_created': return '📤'
    case 'task_completed': return '✅'
    case 'task_failed': return '❌'
    case 'task_cancelled': return '⏹️'
    case 'login': return '🔐'
    default: return '📋'
  }
}

// 格式化时间
const formatTime = (timestamp: string): string => {
  const date = new Date(timestamp)
  const now = new Date()
  const diff = now.getTime() - date.getTime()

  if (diff < 60000) return '刚刚'
  if (diff < 3600000) return `${Math.floor(diff / 60000)}分钟前`
  if (diff < 86400000) return `${Math.floor(diff / 3600000)}小时前`
  if (diff < 604800000) return `${Math.floor(diff / 86400000)}天前`

  return `${date.getMonth() + 1}-${date.getDate()}`
}

// 现代化微信登录（备用方案）
const handleModernWechatLogin = async () => {
  try {
    // 第一步：直接调用 uni.login 获取 code
    uni.showLoading({ title: '登录中...' })

    const loginRes = await new Promise<any>((resolve, reject) => {
      uni.login({
        provider: 'weixin',
        success: resolve,
        fail: reject
      })
    })

    if (!loginRes.code) {
      throw new Error('获取登录凭证失败')
    }

    // 第二步：调用服务端接口获取 openid
    const openidResult = await getWechatOpenid(loginRes.code)

    if (openidResult.errCode !== 0) {
      throw new Error(openidResult.errMsg || '获取openid失败')
    }

    const { openid, isNewUser } = openidResult.data || {}

    if (!openid) {
      throw new Error('获取用户openid失败')
    }

    // 第三步：更新本地用户信息（使用默认头像和昵称）
    userInfo.value.nickname = `用户${openid.slice(-6)}`
    userInfo.value.avatar = '/static/default-avatar.svg'
    userInfo.value.openid = openid
    userInfo.value.isLogin = true

    // 保存用户信息到本地存储
    const userInfoToSave = {
      nickname: userInfo.value.nickname,
      avatar: userInfo.value.avatar,
      openid: userInfo.value.openid,
      loginTime: Date.now(),
      isNewUser: isNewUser
    }

    uni.setStorageSync('userInfo', userInfoToSave)
    uni.hideLoading()

    uni.showToast({
      title: isNewUser ? '注册成功' : '登录成功',
      icon: 'success'
    })

    // 登录成功后加载统计数据
    await loadStats()
    await loadRecentActivities()

  } catch (error: any) {
    uni.hideLoading()
    console.error('现代化微信登录失败:', error)

    uni.showToast({
      title: '登录失败，请重试',
      icon: 'none'
    })
  }
}



// 页面导航
const navigateTo = (url: string) => {
  uni.navigateTo({ url })
}

// 显示关于信息
const showAbout = () => {
  uni.showModal({
    title: '关于我们',
    content: '智能字幕胶囊是一款AI驱动的视频字幕生成工具，支持视频添加字幕、字幕翻译、视频去水印等功能。',
    showCancel: false
  })
}

// 显示设置
const showSettings = () => {
  uni.showActionSheet({
    itemList: ['清除缓存', '推送设置', '隐私设置'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          clearCache()
          break
        case 1:
          uni.showToast({
            title: '推送设置功能开发中',
            icon: 'none'
          })
          break
        case 2:
          uni.showToast({
            title: '隐私设置功能开发中',
            icon: 'none'
          })
          break
      }
    }
  })
}

// 清除缓存
const clearCache = () => {
  uni.showModal({
    title: '清除缓存',
    content: '确定要清除所有缓存数据吗？',
    success: (res) => {
      if (res.confirm) {
        try {
          uni.clearStorageSync()
          uni.showToast({
            title: '缓存清除成功',
            icon: 'success'
          })
        } catch (error) {
          uni.showToast({
            title: '清除失败',
            icon: 'none'
          })
        }
      }
    }
  })
}

// 显示帮助信息
const showHelp = () => {
  uni.showModal({
    title: '帮助与反馈',
    content: '如有问题或建议，请联系我们的客服团队。我们将竭诚为您服务！',
    showCancel: false
  })
}
</script>

<style scoped>

.profile-container {
  min-height: 100vh;
  background-color: #fafafa;
  padding-bottom: 40rpx; /* 为tabbar留出空间 - 减少底部间距 */
}

/* ==================== 用户头部区域 ==================== */
.user-header {
  background: linear-gradient(135deg, #4f46e5, #4338ca);
  padding: 32rpx 32rpx 28rpx; /* 减少上下内边距 */
  position: relative;
  overflow: hidden;
}

.user-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 70% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.user-content {
  position: relative;
  z-index: 1;
  display: flex;
  align-items: center;
  gap: 32rpx;
  margin-bottom: 32rpx;
}

.avatar-section {
  display: flex;
  align-items: center;
  flex: 1;
  gap: 32rpx;
}

.avatar-wrapper {
  position: relative;
  flex-shrink: 0;
}

.avatar {
  width: 128rpx;
  height: 128rpx;
  border-radius: 9999rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 16rpx 64rpx rgba(0, 0, 0, 0.16);
  transition: all 0.2s ease-out;
}

.avatar:hover {
  transform: scale(1.05);
  box-shadow: 0 32rpx 128rpx rgba(0, 0, 0, 0.24);
}

.status-indicator {
  position: absolute;
  bottom: 4rpx;
  right: 4rpx;
  width: 28rpx;
  height: 28rpx;
  border-radius: 9999rpx;
  border: 4rpx solid white;
}

.status-indicator.online {
  background-color: #22c55e;
  box-shadow: 0 0 0 4rpx rgba(34, 197, 94, 0.2);
}

.status-indicator.offline {
  background-color: #a3a3a3;
}

.user-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  gap: 8rpx;
}

.username {
  font-size: 40rpx;
  font-weight: 700;
  color: white;
  line-height: 1.25;
}

.user-id {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.5;
}

.user-badge {
  margin-top: 8rpx;
}

.badge-text {
  display: inline-block;
  padding: 8rpx 16rpx;
  background-color: rgba(255, 255, 255, 0.2);
  color: white;
  font-size: 24rpx;
  font-weight: 500;
  border-radius: 9999rpx;
  backdrop-filter: blur(10rpx);
}

/* 登录区域样式 */
.login-section {
  position: relative;
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  margin-top: 16rpx;
}

.login-btn-centered {
  padding: 24rpx 56rpx;
  font-size: 36rpx;
  font-weight: 700;
  border-radius: 56rpx;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  color: #4f46e5;
  box-shadow:
    0 20rpx 80rpx rgba(0, 0, 0, 0.25),
    0 8rpx 32rpx rgba(79, 70, 229, 0.15),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.9),
    inset 0 -2rpx 0 rgba(0, 0, 0, 0.05);
  border: 4rpx solid rgba(255, 255, 255, 0.4);
  backdrop-filter: blur(30rpx);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  min-width: 320rpx;
  position: relative;
  overflow: hidden;
}

/* 添加发光效果 */
.login-btn-centered::before {
  content: '';
  position: absolute;
  top: -2rpx;
  left: -2rpx;
  right: -2rpx;
  bottom: -2rpx;
  background: linear-gradient(135deg, #4f46e5, #7c3aed, #4f46e5);
  border-radius: 60rpx;
  z-index: -1;
  opacity: 0;
  transition: opacity 0.3s ease-out;
}

/* 添加内部光泽效果 */
.login-btn-centered::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.6s ease-out;
}

.login-btn-centered:hover {
  transform: translateY(-6rpx) scale(1.03);
  box-shadow:
    0 32rpx 120rpx rgba(0, 0, 0, 0.35),
    0 16rpx 64rpx rgba(79, 70, 229, 0.25),
    inset 0 2rpx 0 rgba(255, 255, 255, 1),
    inset 0 -2rpx 0 rgba(0, 0, 0, 0.1);
  background: linear-gradient(135deg, #ffffff 0%, #f1f5f9 100%);
  border-color: rgba(255, 255, 255, 0.6);
  color: #3730a3; /* 确保hover状态下文字颜色更深，提高对比度 */
}

.login-btn-centered:hover::before {
  opacity: 1;
}

.login-btn-centered:hover::after {
  left: 100%;
}

.login-btn-centered:active {
  transform: translateY(-3rpx) scale(1.01);
  box-shadow:
    0 16rpx 64rpx rgba(0, 0, 0, 0.3),
    0 8rpx 32rpx rgba(79, 70, 229, 0.2),
    inset 0 2rpx 0 rgba(255, 255, 255, 0.8),
    inset 0 -2rpx 0 rgba(0, 0, 0, 0.15);
  transition: all 0.15s ease-out;
  color: #312e81; /* 确保active状态下文字颜色最深，提供最佳对比度 */
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); /* 稍微调暗背景 */
}

.login-btn-centered .btn-icon {
  margin-right: 16rpx;
  font-size: 36rpx;
  filter: drop-shadow(0 2rpx 4rpx rgba(79, 70, 229, 0.3));
  transition: all 0.3s ease-out;
}

.login-btn-centered:hover .btn-icon {
  transform: scale(1.1) rotate(5deg);
  filter: drop-shadow(0 4rpx 8rpx rgba(79, 70, 229, 0.4));
}

/* 次要登录按钮样式 */
.btn-secondary {
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  border: 2rpx solid #cbd5e1;
  color: #334155; /* 更深的文字颜色，提高对比度 */
}

.btn-secondary:hover {
  background: linear-gradient(135deg, #f1f5f9 0%, #d1d5db 100%);
  border-color: #94a3b8;
  transform: translateY(-2rpx);
  color: #1e293b; /* hover状态下更深的文字颜色 */
}

.btn-secondary:active {
  background: linear-gradient(135deg, #e2e8f0 0%, #cbd5e1 100%);
  border-color: #64748b;
  transform: translateY(0);
  color: #0f172a; /* active状态下最深的文字颜色 */
}

.login-tip {
  font-size: 28rpx;
  color: rgba(255, 255, 255, 0.9);
  text-align: center;
  font-weight: 600;
  text-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  letter-spacing: 1rpx;
}

/* ==================== 统计信息区域 ==================== */
.stats-section {
  padding: 0 32rpx;
  margin-bottom: 32rpx; /* 减少底部间距 */
  padding-top: 40rpx;
}

.section-header {
  margin-bottom: 24rpx; /* 减少标题与内容间距 */
  text-align: center;
}

.section-title {
  display: block;
  font-size: 40rpx; /* 稍微减小标题字体 */
  font-weight: 700;
  color: #171717;
  margin-bottom: 4rpx; /* 减少标题与副标题间距 */
}

.section-subtitle {
  display: block;
  font-size: 26rpx; /* 稍微减小副标题字体 */
  color: #525252;
  line-height: 1.5;
}

.stats-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx; /* 减少统计卡片间距 */
}

.stats-loading {
  background: white;
  border-radius: 24rpx;
  padding: 60rpx 32rpx;
  text-align: center;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
}

.loading-text {
  font-size: 28rpx;
  color: #6b7280;
  animation: pulse 2s ease-in-out infinite;
}

/* 主要统计 - 更突出的展示 */
.primary-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx; /* 减少网格间距 */
}

.primary-stat-item {
  background: white;
  border-radius: 32rpx; /* 减小圆角 */
  padding: 32rpx; /* 减少内边距 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  gap: 24rpx; /* 减少图标与内容间距 */
  transition: all 0.2s ease-out;
}

.primary-stat-item:hover {
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.12);
  transform: translateY(-2rpx);
}

/* 次要统计 - 更紧凑的展示 */
.secondary-stats {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx; /* 减少网格间距 */
}

.secondary-stat-item {
  background: white;
  border-radius: 24rpx; /* 减小圆角 */
  padding: 24rpx; /* 减少内边距 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  display: flex;
  align-items: center;
  gap: 16rpx; /* 减少图标与内容间距 */
  transition: all 0.2s ease-out;
}

.secondary-stat-item:hover {
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.08);
  transform: translateY(-1rpx);
}

/* 统计图标样式 */
.stat-icon-wrapper {
  width: 64rpx;
  height: 64rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.stat-icon-wrapper.primary {
  background: linear-gradient(135deg, #6366f1, #4f46e5);
}

.stat-icon-wrapper.success {
  background: linear-gradient(135deg, #22c55e, #16a34a);
}

.stat-icon-wrapper.info {
  background: linear-gradient(135deg, #3b82f6, #2563eb);
}

.stat-icon-wrapper.warning {
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.secondary-stat-item .stat-icon-wrapper {
  width: 48rpx;
  height: 48rpx;
  border-radius: 16rpx;
}

.stat-icon {
  font-size: 32rpx;
}

.secondary-stat-item .stat-icon {
  font-size: 24rpx;
}

.stat-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.stat-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #171717;
  line-height: 1.25;
}

.secondary-stat-item .stat-number {
  font-size: 36rpx;
  font-weight: 600;
}

.stat-label {
  font-size: 28rpx;
  color: #525252;
  line-height: 1.5;
}

.secondary-stat-item .stat-label {
  font-size: 24rpx;
}

/* 最近活动样式 */
.recent-activities {
  background: white;
  border-radius: 24rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  margin-top: 20rpx;
}

.activity-header {
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.activity-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16rpx;
  padding: 12rpx 0;
}

.activity-icon {
  width: 40rpx;
  height: 40rpx;
  border-radius: 20rpx;
  background: #f3f4f6;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  flex-shrink: 0;
}

.activity-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.activity-text {
  font-size: 26rpx;
  color: #1f2937;
  line-height: 1.4;
}

.activity-time {
  font-size: 22rpx;
  color: #9ca3af;
}

/* 未登录状态提示样式 */
.login-prompt-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 32rpx 0;
}

.login-prompt-card {
  background: white;
  border-radius: 32rpx;
  padding: 48rpx 32rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  max-width: 400rpx;
  width: 100%;
}

.prompt-icon-wrapper {
  width: 96rpx;
  height: 96rpx;
  border-radius: 48rpx;
  background: linear-gradient(135deg, #f3f4f6, #e5e7eb);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 24rpx;
}

.prompt-icon {
  font-size: 48rpx;
}

.prompt-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.prompt-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #171717;
  line-height: 1.25;
}

.prompt-desc {
  font-size: 28rpx;
  color: #525252;
  line-height: 1.5;
}

/* ==================== 功能菜单区域 ==================== */
.menu-section {
  padding: 0 32rpx;
  margin-bottom: 32rpx; /* 减少底部间距 */
}

.menu-container {
  background: white;
  border-radius: 32rpx; /* 减小圆角 */
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.menu-item {
  display: flex;
  align-items: center;
  padding: 32rpx 40rpx; /* 减少内边距 */
  border-bottom: 1rpx solid #f5f5f5;
  transition: all 0.2s ease-out;
  cursor: pointer;
  position: relative;
}

.menu-item:last-child {
  border-bottom: none;
}

.menu-item:hover {
  background-color: #f5f5f5;
}

.menu-item:active {
  background-color: #e5e5e5;
  transform: scale(0.98);
}

/* 添加微妙的点击反馈 */
.menu-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(99, 102, 241, 0.05), transparent);
  opacity: 0;
  transition: opacity 0.15s ease-out;
}

.menu-item:active::after {
  opacity: 1;
}

.menu-icon-wrapper {
  width: 56rpx;
  height: 56rpx;
  border-radius: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 32rpx;
  flex-shrink: 0;
  transition: all 0.2s ease-out;
}

.menu-icon-wrapper.history {
  background: linear-gradient(135deg, #e0e7ff, #c7d2fe);
}

.menu-icon-wrapper.settings {
  background: linear-gradient(135deg, #f5f5f5, #e5e5e5);
}

.menu-icon-wrapper.about {
  background: linear-gradient(135deg, #eff6ff, #dbeafe);
}

.menu-icon-wrapper.help {
  background: linear-gradient(135deg, #fffbeb, #fef3c7);
}

.menu-item:hover .menu-icon-wrapper {
  transform: scale(1.1);
}

.menu-icon {
  font-size: 36rpx;
}

.menu-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.menu-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #171717;
  line-height: 1.25;
}

.menu-desc {
  font-size: 28rpx;
  color: #525252;
  line-height: 1.5;
}

.menu-arrow {
  width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease-out;
}

.menu-item:hover .menu-arrow {
  background-color: #e0e7ff;
  transform: translateX(4rpx);
}

.arrow-icon {
  font-size: 32rpx;
  color: #a3a3a3;
  font-weight: 600;
}

.menu-item:hover .arrow-icon {
  color: #4f46e5;
}

/* ==================== 底部版本信息区域 ==================== */
.footer-section {
  padding: 26rpx 32rpx 24rpx;
}

.version-info {
  text-align: center;
  padding: 64rpx;
  background: white;
  border-radius: 48rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f5f5f5;
}

.version-text {
  display: block;
  font-size: 28rpx;
  color: #525252;
  font-weight: 500;
  margin-bottom: 16rpx;
  line-height: 1.5;
}

.copyright-text {
  display: block;
  font-size: 24rpx;
  color: #a3a3a3;
  line-height: 1.5;
}

/* ==================== 响应式适配 ==================== */
/* 小屏幕适配 */
@media (max-width: 375px) {
  .primary-stats,
  .secondary-stats {
    grid-template-columns: 1fr;
  }

  .primary-stat-item,
  .secondary-stat-item {
    padding: 32rpx;
  }

  .avatar {
    width: 112rpx;
    height: 112rpx;
  }

  .username {
    font-size: 36rpx;
  }

  .login-btn-centered {
    min-width: 280rpx;
    padding: 20rpx 48rpx;
    font-size: 32rpx;
  }

  .login-tip {
    font-size: 26rpx;
  }
}

/* 大屏幕优化 */
@media (min-width: 768px) {
  .profile-container {
    max-width: 750rpx;
    margin: 0 auto;
  }

  .stats-container {
    flex-direction: row;
    gap: 48rpx;
  }

  .primary-stats,
  .secondary-stats {
    flex: 1;
    grid-template-columns: 1fr;
  }
}

/* ==================== 动画效果 ==================== */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.stats-section,
.menu-section,
.footer-section {
  animation: fadeInUp 0.6s ease-out;
}

.stats-section {
  animation-delay: 0.1s;
}

.menu-section {
  animation-delay: 0.2s;
}

.footer-section {
  animation-delay: 0.3s;
}

/* 加载状态动画 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.loading {
  animation: pulse 2s ease-in-out infinite;
}
</style>
