// uniCloud云函数：查询任务状态
"use strict";

/**
 * 查询视频处理任务状态
 *
 * @param {Object} event
 * @param {string} event.taskId - 任务ID（必需）
 * @param {string} event.openid - 用户openid（用于权限验证，可选，如果不提供则不进行用户权限验证）
 * @returns {Object} 任务状态信息
 */
exports.main = async (event, context) => {
  try {
    const { taskId, openid } = event;
    const { CLIENTUA, CLIENTIP } = context;

    console.log("get-task-status 云函数被调用，参数：", { taskId, hasOpenid: !!openid });

    // 参数验证
    if (!taskId) {
      console.error("参数验证失败：缺少taskId参数");
      return {
        errCode: "INVALID_PARAM",
        errMsg: "缺少必要参数：taskId",
      };
    }

    // 验证taskId格式（简单验证）
    if (typeof taskId !== "string" || taskId.length === 0) {
      console.error("参数验证失败：taskId格式不正确");
      return {
        errCode: "INVALID_PARAM",
        errMsg: "taskId格式不正确",
      };
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    console.log("准备查询任务状态，taskId：", taskId);

    // 查询任务信息
    let taskQuery = tasksCollection.where({
      _id: taskId,
    });

    // 如果提供了openid，添加用户权限验证
    if (openid) {
      // 首先查询用户ID
      const usersCollection = db.collection("users");
      const userResult = await usersCollection
        .where({
          openid: openid,
        })
        .field({
          _id: true,
        })
        .limit(1)
        .get();

      if (!userResult.data || userResult.data.length === 0) {
        console.error("用户验证失败：用户不存在", openid);
        return {
          errCode: "USER_NOT_FOUND",
          errMsg: "用户不存在",
        };
      }

      const userId = userResult.data[0]._id;
      console.log("用户验证通过，userId：", userId);

      // 添加用户权限过滤，优化查询性能
      taskQuery = taskQuery.where({
        userId: userId,
      });
    }

    // 只查询必要的字段，提高查询性能
    const taskResult = await taskQuery
      .field({
        _id: true,
        status: true,
        createTime: true,
        updateTime: true,
        finalVideoUrl: true,
        errorMessage: true,
        // 新增文件相关字段
        fileName: true,
        fileSize: true,
        duration: true,
        ossUrl: true,
      })
      .limit(1)
      .get();

    if (!taskResult.data || taskResult.data.length === 0) {
      console.error("任务查询失败：任务不存在或无权限访问", taskId);
      return {
        errCode: "TASK_NOT_FOUND",
        errMsg: "任务不存在或无权限访问",
      };
    }

    const taskData = taskResult.data[0];
    console.log("任务查询成功，状态：", taskData.status);

    // 构建返回数据，只返回必要的字段，避免敏感信息泄露
    const responseData = {
      taskId: taskData._id,
      status: taskData.status,
      createTime: taskData.createTime,
      updateTime: taskData.updateTime,
      // 新增文件相关信息
      fileInfo: {
        fileName: taskData.fileName || '',
        fileSize: taskData.fileSize || 0,
        duration: taskData.duration || 0,
        ossUrl: taskData.ossUrl || ''
      }
    };

    // 根据任务状态返回相应的额外信息
    switch (taskData.status) {
      case "completed":
        // 任务完成，返回最终视频地址
        if (taskData.finalVideoUrl) {
          responseData.finalVideoUrl = taskData.finalVideoUrl;
        }
        break;

      case "failed":
        // 任务失败，返回错误信息
        if (taskData.errorMessage) {
          responseData.errorMessage = taskData.errorMessage;
        }
        break;

      case "extracting_audio":
      case "recognizing":
      case "translating":
      case "merging":
        // 处理中状态，返回进度信息
        responseData.progress = getProgressByStatus(taskData.status);
        break;

      default:
        // 其他状态
        break;
    }

    return {
      errCode: 0,
      code: 200,
      errMsg: "查询成功",
      data: responseData,
    };
  } catch (error) {
    console.error("get-task-status 云函数执行错误：", error);

    return {
      errCode: "GET_TASK_STATUS_FAILED",
      code: 500,
      errMsg: "查询任务状态失败: " + error.message,
    };
  }
};

/**
 * 根据任务状态返回进度百分比
 * @param {string} status 任务状态
 * @returns {number} 进度百分比
 */
function getProgressByStatus(status) {
  const progressMap = {
    uploading: 10,
    extracting_audio: 25,
    recognizing: 50,
    translating: 75,
    merging: 90,
    completed: 100,
    failed: 0,
  };

  return progressMap[status] || 0;
}
