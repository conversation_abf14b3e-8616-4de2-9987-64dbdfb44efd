<template>
  <view class="process-container">
    <!-- 进度卡片 -->
    <view class="progress-card">
      <view class="progress-header">
        <text class="progress-title">处理进度</text>
        <text class="progress-status">{{ statusText }}</text>
      </view>
      
      <view class="progress-bar-container">
        <view class="progress-bar">
          <view class="progress-fill" :style="{ width: progress + '%' }"></view>
        </view>
        <text class="progress-text">{{ progress }}%</text>
      </view>

      <view class="progress-steps">
        <view class="step" :class="{ active: currentStep >= 1, completed: currentStep > 1 }">
          <view class="step-icon">📤</view>
          <text class="step-text">上传视频</text>
        </view>
        <view class="step" :class="{ active: currentStep >= 2, completed: currentStep > 2 }">
          <view class="step-icon">🎤</view>
          <text class="step-text">语音识别</text>
        </view>
        <view class="step" :class="{ active: currentStep >= 3, completed: currentStep > 3 }">
          <view class="step-icon">📝</view>
          <text class="step-text">生成字幕</text>
        </view>
        <view class="step" :class="{ active: currentStep >= 4, completed: currentStep > 4 }">
          <view class="step-icon">🎬</view>
          <text class="step-text">合成视频</text>
        </view>
      </view>
    </view>

    <!-- 视频信息 -->
    <view class="video-info-card">
      <view class="card-header">
        <text class="card-title">视频信息</text>
      </view>
      <view class="card-body">
        <view class="info-item">
          <text class="info-label">文件名:</text>
          <text class="info-value">{{ videoInfo.fileName || '加载中...' }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">文件大小:</text>
          <text class="info-value">{{ formatFileSize(videoInfo.fileSize) }}</text>
        </view>
        <view class="info-item">
          <text class="info-label">视频时长:</text>
          <text class="info-value">{{ formatDuration(videoInfo.duration) }}</text>
        </view>
      </view>
    </view>

    <!-- 操作按钮 -->
    <view class="action-section">
      <button v-if="isCompleted" @click="viewResult" class="btn btn-primary">
        查看结果
      </button>
      <button v-else-if="isCancelling" class="btn btn-disabled" disabled>
        正在取消...
      </button>
      <button v-else @click="cancelProcess" class="btn btn-secondary">
        取消处理
      </button>
    </view>

    <!-- 底部空间 -->
    <view class="bottom-space"></view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted } from 'vue'

// 定时器状态管理
const timersPaused = ref(false)
const lastPollingTime = ref(0)

// 响应式数据
const progress = ref(0)
const currentStep = ref(2)
const statusText = ref('正在处理中...')
const isCompleted = ref(false)
const taskId = ref('')

// 视频信息
const videoInfo = ref({
  fileName: '',
  fileSize: 0,
  duration: 0
})

// 状态轮询定时器
let statusPollingTimer: number | null = null
// 页面可见性状态
const isPageVisible = ref(true)
// 是否正在取消任务
const isCancelling = ref(false)

onMounted(() => {
  // 初始化进度状态
  initializeProgress()

  // 添加页面可见性监听
  setupVisibilityListener()

  // 获取页面参数
  const pages = getCurrentPages()
  const currentPage = pages[pages.length - 1] as any
  const options = currentPage.options || {}

  if (options.taskId) {
    taskId.value = options.taskId
    console.log('接收到任务ID:', taskId.value)

    // 开始查询任务状态
    startTaskStatusPolling()
  } else {
    // 显示错误提示
    uni.showModal({
      title: '参数错误',
      content: '缺少任务ID参数，请重新上传视频',
      showCancel: false,
      success: () => {
        uni.navigateBack()
      }
    })
  }
})

// 页面可见性监听器
const setupVisibilityListener = () => {
  // 监听页面显示/隐藏
  uni.onAppShow(() => {
    console.log('应用显示，从后台模式恢复')
    isPageVisible.value = true
    timersPaused.value = false

    if (!isCompleted.value && !isCancelling.value) {
      // 从后台模式恢复
      resumeFromBackgroundMode()
      // 恢复轮询
      resumePollingIfNeeded()
    }
  })

  uni.onAppHide(() => {
    console.log('应用隐藏，切换到后台模式')
    isPageVisible.value = false
    timersPaused.value = true
    lastPollingTime.value = Date.now()

    // 切换到后台模式
    switchToBackgroundMode()
  })
}

// 移除可见性监听器
const removeVisibilityListener = () => {
  // uni-app的监听器会在页面销毁时自动清理
  console.log('移除可见性监听器')
}

// 切换到后台模式
const switchToBackgroundMode = async () => {
  if (!taskId.value || isCompleted.value || isCancelling.value) {
    return
  }

  try {
    console.log('将任务切换到后台模式:', taskId.value)

    // 调用云函数将任务标记为后台模式
    const result = await uniCloud.callFunction({
      name: 'update-task-background-mode',
      data: {
        taskId: taskId.value,
        backgroundMode: true
      }
    })

    if (result.result.code === 200) {
      console.log('任务已切换到后台模式')
    } else {
      console.error('切换后台模式失败:', result.result.message)
    }
  } catch (error) {
    console.error('切换后台模式失败:', error)
  }
}

// 从后台模式恢复
const resumeFromBackgroundMode = async () => {
  if (!taskId.value) {
    return
  }

  try {
    console.log('从后台模式恢复任务:', taskId.value)

    // 调用云函数将任务标记为前台模式
    const result = await uniCloud.callFunction({
      name: 'update-task-background-mode',
      data: {
        taskId: taskId.value,
        backgroundMode: false
      }
    })

    if (result.result.code === 200) {
      console.log('任务已从后台模式恢复')
    } else {
      console.error('恢复前台模式失败:', result.result.message)
    }
  } catch (error) {
    console.error('恢复前台模式失败:', error)
  }
}

// 直接更新进度函数
const updateProgress = (newValue: number) => {
  progress.value = Math.round(Math.max(0, Math.min(100, newValue)) * 10) / 10
}

// 初始化进度状态
const initializeProgress = () => {
  progress.value = 0
  currentStep.value = 1
  statusText.value = '正在初始化...'
  isCompleted.value = false
  isCancelling.value = false
}

onUnmounted(() => {
  console.log('页面卸载，清理所有定时器和监听器')
  // 页面卸载时，将任务转入后台模式
  switchToBackgroundMode()
  cleanupAllTimers()
  removeVisibilityListener()
})

// 恢复轮询的辅助函数
const resumePollingIfNeeded = () => {
  console.log('检查是否需要恢复轮询')

  // 如果任务未完成且未取消，且没有正在运行的轮询，则恢复轮询
  if (!isCompleted.value && !isCancelling.value && taskId.value && !statusPollingTimer) {
    console.log('恢复任务状态轮询')

    // 检查是否需要立即轮询（如果暂停时间超过轮询间隔）
    const pauseDuration = Date.now() - lastPollingTime.value
    if (pauseDuration > 10000) { // 超过10秒，立即查询一次
      checkTaskStatus()
    }

    // 恢复状态轮询
    statusPollingTimer = setInterval(async () => {
      if (!timersPaused.value) {
        await checkTaskStatus()
      }
    }, 10000)
  }
}

// 停止轮询的统一函数
const stopTaskPolling = () => {
  if (statusPollingTimer) {
    clearInterval(statusPollingTimer)
    statusPollingTimer = null
    console.log('任务轮询已停止')
  }
}

// 清理所有定时器的统一函数
const cleanupAllTimers = () => {
  console.log('清理所有定时器')

  // 使用统一的停止轮询函数
  stopTaskPolling()

  // 重置状态
  timersPaused.value = false
}





// 格式化文件大小
const formatFileSize = (size: number): string => {
  if (!size) return '0B'
  if (size < 1024) return size + 'B'
  if (size < 1024 * 1024) return (size / 1024).toFixed(1) + 'KB'
  if (size < 1024 * 1024 * 1024) return (size / (1024 * 1024)).toFixed(1) + 'MB'
  return (size / (1024 * 1024 * 1024)).toFixed(1) + 'GB'
}

// 格式化时长
const formatDuration = (seconds: number): string => {
  if (!seconds) return '00:00'
  const mins = Math.floor(seconds / 60)
  const secs = seconds % 60
  return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
}

// 开始任务状态轮询
const startTaskStatusPolling = async () => {
  try {
    statusText.value = '正在连接服务器...'

    // 设置初始进度
    updateProgress(3)

    statusText.value = '正在查询任务状态...'

    // 立即查询一次
    await checkTaskStatus()

    // 每10秒轮询一次，但检查暂停状态
    statusPollingTimer = setInterval(async () => {
      if (!timersPaused.value) {
        await checkTaskStatus()
      }
    }, 10000)

  } catch (error) {
    console.error('开始状态轮询失败:', error)
    statusText.value = '查询状态失败'
  }
}

// 检查任务状态
const checkTaskStatus = async () => {
  try {
    console.log('查询任务状态:', taskId.value)

    // 首先调用通用的任务状态查询
    const statusResult = await uniCloud.callFunction({
      name: 'get-task-status',
      data: {
        taskId: taskId.value
      }
    })

    console.log('任务状态查询结果:', statusResult)

    // 如果任务处于识别阶段，额外调用 poll-paraformer-tasks 获取最新状态
    if (statusResult.result.code === 200 && statusResult.result.data.status === 'recognizing') {
      try {
        const pollResult = await uniCloud.callFunction({
          name: 'poll-paraformer-tasks',
          data: {
            taskId: taskId.value,
            mode: 'query'
          }
        })

        console.log('Paraformer轮询结果:', pollResult)

        // 如果轮询返回了更新的状态，使用轮询结果
        if (pollResult.result.code === 200 && pollResult.result.data.status) {
          const pollData = pollResult.result.data
          if (pollData.status === 'success' || pollData.status === 'failed') {
            // 识别完成或失败，更新显示
            updateTaskProgress(pollData.status === 'success' ? 'translating' : 'failed',
                             pollData.status === 'success' ? 80 : 0)
            return
          }
        }
      } catch (pollError) {
        console.warn('Paraformer状态轮询失败:', pollError)
        // 轮询失败不影响主流程，继续使用通用状态
      }
    }

    // 如果任务处于MPS处理阶段，额外调用 poll-mps-tasks 获取最新状态
    if (statusResult.result.code === 200 &&
        (statusResult.result.data.status === 'extracting_audio' || statusResult.result.data.status === 'merging')) {
      try {
        const mpsResult = await uniCloud.callFunction({
          name: 'poll-mps-tasks',
          data: {
            taskId: taskId.value,
            mode: 'query'
          }
        })

        console.log('MPS轮询结果:', mpsResult)

        // 如果轮询返回了更新的状态，使用轮询结果
        if (mpsResult.result.code === 200 && mpsResult.result.data.status) {
          const mpsData = mpsResult.result.data
          if (mpsData.status === 'success') {
            // MPS任务成功，根据当前状态推进到下一阶段
            const currentStatus = statusResult.result.data.status
            if (currentStatus === 'extracting_audio') {
              // 音频提取完成，进入语音识别阶段
              updateTaskProgress('recognizing', 60)
            } else if (currentStatus === 'merging') {
              // 视频合成完成，任务完成
              updateTaskProgress('completed', 100)
            }
            return
          } else if (mpsData.status === 'failed') {
            // MPS任务失败
            updateTaskProgress('failed', 0)
            return
          }
          // 如果状态是 'running'，继续使用通用状态
        }
      } catch (mpsError) {
        console.warn('MPS状态轮询失败:', mpsError)
        // 轮询失败不影响主流程，继续使用通用状态
      }
    }

    // 使用通用状态结果
    const result = statusResult

    if (result.result.code === 200) {
      const { status, progress: taskProgress, fileInfo } = result.result.data

      // 更新视频信息
      if (fileInfo) {
        videoInfo.value = {
          fileName: fileInfo.fileName || '',
          fileSize: fileInfo.fileSize || 0,
          duration: fileInfo.duration || 0
        }
      }

      // 根据状态更新界面（会自动检查并停止轮询）
      updateTaskProgress(status, taskProgress)

      // 如果任务已结束，确保轮询已停止
      if (isTaskFinished(status)) {
        console.log(`任务已结束，状态：${status}，确保轮询已停止`)
        stopTaskPolling()
      }
    } else {
      console.error('查询任务状态失败:', result.result.message)
      statusText.value = '查询状态失败'
    }

  } catch (error) {
    console.error('查询任务状态失败:', error)
    statusText.value = '查询状态失败'
  }
}

// 检查是否为结束状态的辅助函数
const isTaskFinished = (status: string): boolean => {
  // 定义所有任务结束状态，当任务达到这些状态时应立即停止轮询
  const finishedStates = ['completed', 'failed', 'cancelled', 'deleted']
  return finishedStates.includes(status)
}

// 更新任务进度的辅助函数
const updateTaskProgress = (status: string, progressValue: number) => {
  // 首先检查是否为结束状态，如果是则停止轮询
  if (isTaskFinished(status)) {
    stopTaskPolling()
  }

  switch (status) {
    case 'uploading':
      currentStep.value = 1
      updateProgress(progressValue || 25)
      statusText.value = '正在上传视频...'
      break
    case 'extracting_audio':
      currentStep.value = 2
      updateProgress(progressValue || 40)
      statusText.value = '正在提取音频...'
      break
    case 'recognizing':
      currentStep.value = 2
      updateProgress(progressValue || 60)
      statusText.value = '正在识别语音...'
      break
    case 'translating':
      currentStep.value = 3
      updateProgress(progressValue || 80)
      statusText.value = '正在生成字幕...'
      break
    case 'merging':
      currentStep.value = 4
      updateProgress(progressValue || 90)
      statusText.value = '正在合成视频...'
      break
    case 'completed':
      currentStep.value = 4
      updateProgress(100)
      statusText.value = '处理完成！'
      isCompleted.value = true
      // 轮询已在函数开头统一停止，无需重复停止
      break
    case 'failed':
      statusText.value = '处理失败'
      // 轮询已在函数开头统一停止，无需重复停止

      uni.showToast({
        title: '处理失败',
        icon: 'error'
      })
      break
    case 'cancelled':
      statusText.value = '任务已取消'
      updateProgress(0)
      // 轮询已在函数开头统一停止，无需重复停止
      break
    case 'deleted':
      statusText.value = '任务已删除'
      updateProgress(0)
      // 轮询已在函数开头统一停止，无需重复停止
      break
    default:
      statusText.value = '未知状态'
      break
  }
}

// 查看结果
const viewResult = () => {
  // 检查任务ID是否存在
  if (!taskId.value) {
    console.error('查看结果失败：任务ID不存在')
    uni.showToast({
      title: '任务ID不存在',
      icon: 'none'
    })
    return
  }

  // 检查任务是否真的完成
  if (!isCompleted.value) {
    console.error('查看结果失败：任务尚未完成，当前状态:', statusText.value)
    uni.showToast({
      title: '任务尚未完成',
      icon: 'none'
    })
    return
  }

  // 检查进度是否为100%
  if (progress.value < 100) {
    console.warn('查看结果警告：进度未达到100%，当前进度:', progress.value)
    uni.showModal({
      title: '提示',
      content: '任务可能尚未完全完成，确定要查看结果吗？',
      success: (res) => {
        if (res.confirm) {
          navigateToResult()
        }
      }
    })
    return
  }

  // 直接跳转
  navigateToResult()
}

// 跳转到结果页面的辅助函数
const navigateToResult = () => {
  // 构建跳转URL，携带必要参数
  const url = `/pages/result/result?taskId=${encodeURIComponent(taskId.value)}`

  console.log('跳转到结果页面，参数:', {
    taskId: taskId.value,
    status: statusText.value,
    progress: progress.value,
    url: url
  })

  uni.navigateTo({
    url: url,
    success: () => {
      console.log('成功跳转到结果页面')
    },
    fail: (error) => {
      console.error('跳转到结果页面失败:', error)
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      })
    }
  })
}

// 取消处理 - 完善版
const cancelProcess = () => {
  // 防止重复点击
  if (isCancelling.value) {
    uni.showToast({
      title: '正在取消中...',
      icon: 'loading'
    })
    return
  }

  uni.showModal({
    title: '确认取消处理',
    content: '取消后当前处理进度将丢失，确定要取消吗？',
    confirmText: '确定取消',
    cancelText: '继续处理',
    confirmColor: '#ff4757',
    success: (res) => {
      if (res.confirm) {
        performCancelTask()
      }
    }
  })
}

// 执行取消任务的具体操作
const performCancelTask = async () => {
  try {
    isCancelling.value = true
    statusText.value = '正在取消处理...'

    // 立即停止所有动画和轮询
    cleanupAllTimers()

    // 重置进度
    updateProgress(0)

    console.log('开始取消任务:', taskId.value)

    // 调用后端取消任务的云函数
    const cancelResult = await uniCloud.callFunction({
      name: 'cancel-task',
      data: {
        taskId: taskId.value
      }
    })

    console.log('取消任务结果:', cancelResult)

    if (cancelResult.result.code === 200) {
      // 取消成功
      statusText.value = '已取消处理'

      uni.showToast({
        title: '已取消处理',
        icon: 'success',
        duration: 2000
      })

      // 延迟返回上一页
      setTimeout(() => {
        uni.navigateBack({
          fail: () => {
            // 如果返回失败，跳转到首页
            uni.reLaunch({
              url: '/pages/index/index'
            })
          }
        })
      }, 2000)

    } else {
      // 取消失败，但仍然返回
      console.error('取消任务失败:', cancelResult.result.message)

      uni.showModal({
        title: '取消失败',
        content: '无法取消任务，但您可以离开此页面。处理可能会在后台继续。',
        showCancel: false,
        success: () => {
          uni.navigateBack({
            fail: () => {
              uni.reLaunch({
                url: '/pages/index/index'
              })
            }
          })
        }
      })
    }

  } catch (error) {
    console.error('取消任务时发生错误:', error)

    uni.showModal({
      title: '取消失败',
      content: '网络错误，无法取消任务。您可以离开此页面，处理可能会在后台继续。',
      showCancel: false,
      success: () => {
        uni.navigateBack({
          fail: () => {
            uni.reLaunch({
              url: '/pages/index/index'
            })
          }
        })
      }
    })

  } finally {
    isCancelling.value = false
  }
}
</script>

<style scoped>
.process-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f4ff 0%, #e0e7ff 100%);
  padding: 32rpx;
  padding-bottom: 120rpx;
}

.progress-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32rpx;
}

.progress-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #1f2937;
}

.progress-status {
  font-size: 28rpx;
  color: #6366f1;
  font-weight: 500;
}

.progress-bar-container {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 48rpx;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background: #e5e7eb;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-fill {
  height: 100%;
  background: #6366f1;
  border-radius: 6rpx;
}

.progress-bar {
  flex: 1;
  height: 12rpx;
  background: #e5e7eb;
  border-radius: 6rpx;
  overflow: hidden;
}

.progress-text {
  font-size: 28rpx;
  font-weight: 600;
  color: #1f2937;
  min-width: 80rpx;
  font-variant-numeric: tabular-nums; /* 等宽数字，避免数字变化时的跳动 */
  text-align: right;
}

.progress-steps {
  display: flex;
  justify-content: space-between;
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  opacity: 0.4;
  transition: opacity 0.3s ease;
}

.step.active {
  opacity: 1;
}

.step.completed {
  opacity: 0.8;
}

.step-icon {
  font-size: 32rpx;
  margin-bottom: 8rpx;
}

.step-text {
  font-size: 24rpx;
  color: #6b7280;
  text-align: center;
}

.video-info-card {
  background: white;
  border-radius: 24rpx;
  padding: 32rpx;
  margin-bottom: 32rpx;
  box-shadow: 0 4rpx 24rpx rgba(0, 0, 0, 0.06);
}

.card-header {
  margin-bottom: 24rpx;
}

.card-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
}

.card-body {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.info-label {
  font-size: 28rpx;
  color: #6b7280;
}

.info-value {
  font-size: 28rpx;
  color: #1f2937;
  font-weight: 500;
}

.action-section {
  display: flex;
  justify-content: center;
}

.btn {
  padding: 24rpx 48rpx;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  min-width: 200rpx;
}

.btn-primary {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
}

.btn-secondary {
  background: #f3f4f6;
  color: #6b7280;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  background: #e5e7eb;
}

.btn-disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.6;
}

.bottom-space {
  height: 80rpx;
}
</style>
