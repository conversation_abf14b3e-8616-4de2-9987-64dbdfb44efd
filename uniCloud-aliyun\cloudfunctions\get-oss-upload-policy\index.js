'use strict';

const OSS = require('ali-oss');
const path = require('path');
const createConfig = require('uni-config-center');

exports.main = async (event, context) => {
  console.log('get-oss-upload-policy 云函数被调用，参数：', event);

  try {
    // 参数验证
    const { fileName, fileSize, duration, openid, sourceLanguage = 'auto', targetLanguage = 'zh' } = event;
    if (!fileName) {
      console.error('参数验证失败：缺少fileName参数');
      return {
        code: 400,
        message: '缺少必要参数：fileName'
      };
    }

    if (!openid) {
      console.error('参数验证失败：缺少openid参数');
      return {
        code: 400,
        message: '缺少必要参数：openid'
      };
    }

    if (!fileSize || typeof fileSize !== 'number') {
      console.error('参数验证失败：fileSize参数无效');
      return {
        code: 400,
        message: '缺少必要参数：fileSize'
      };
    }

    if (!duration || typeof duration !== 'number') {
      console.error('参数验证失败：duration参数无效');
      return {
        code: 400,
        message: '缺少必要参数：duration'
      };
    }

    // 验证文件名格式（简单验证）
    if (typeof fileName !== 'string' || fileName.length === 0) {
      console.error('参数验证失败：fileName格式不正确');
      return {
        code: 400,
        message: '文件名格式不正确'
      };
    }
    
    // 获取数据库引用
    const db = uniCloud.database();
    const usersCollection = db.collection('users');

    // 验证用户是否存在
    const userResult = await usersCollection.where({
      openid: openid
    }).field({
      _id: true
    }).get();

    if (!userResult.data || userResult.data.length === 0) {
      console.error('用户验证失败：用户不存在', openid);
      return {
        code: 401,
        message: '用户不存在，请先登录'
      };
    }

    const userId = userResult.data[0]._id;
    console.log('用户验证通过，userId：', userId);

    // 先创建任务记录，获取taskId用于文件命名
    const tasksCollection = db.collection('tasks');
    const taskResult = await tasksCollection.add({
      userId: userId,
      fileName: fileName,
      fileSize: fileSize || 0,
      duration: duration || 0,
      sourceLanguage: sourceLanguage,
      targetLanguage: targetLanguage,
      status: 'uploading',
      createTime: new Date(),
      updateTime: new Date()
    });

    const taskId = taskResult.id;
    console.log('创建任务记录成功，taskId：', taskId);

    // 阿里云OSS配置 - 使用uni-config-center配置中心获取
    const aliyunConfig = createConfig({
      pluginId: 'aliyun-oss',
      defaultConfig: {
        region: 'oss-cn-shanghai',
        bucket: 'video--tanslate'
      }
    });

    const accessKeyId = aliyunConfig.config('accessKeyId');
    const accessKeySecret = aliyunConfig.config('accessKeySecret');
    const region = aliyunConfig.config('region');
    const bucketName = aliyunConfig.config('bucket');

    // 验证必需的配置
    if (!accessKeyId || !accessKeySecret) {
      console.error('阿里云配置检查失败：', {
        hasAccessKeyId: !!accessKeyId,
        hasAccessKeySecret: !!accessKeySecret,
        region,
        bucketName
      });
      return {
        code: 500,
        message: '服务配置错误，请配置阿里云访问密钥'
      };
    }

    console.log('阿里云配置检查通过，region:', region, 'bucket:', bucketName);

    // 使用taskId生成文件名，确保与数据库记录一致
    const timestamp = Date.now();
    const fileExtension = path.extname(fileName);
    const objectKey = `video/task_${taskId}_${timestamp}${fileExtension}`;
    
    // OSS配置信息
    const host = `https://${bucketName}.${region}.aliyuncs.com`;
    
    // 初始化OSS客户端
    const client = new OSS({
      accessKeyId: accessKeyId,
      accessKeySecret: accessKeySecret,
      bucket: bucketName,
      region: region,
    });

    // 生成 Post Policy (上传策略)
    const expire = 600; // 策略有效期10分钟 (600秒)
    const expireTime = new Date().getTime() + expire * 1000;
    
    const policy = {
      expiration: new Date(expireTime).toISOString(),
      conditions: [
        // 精确匹配Bucket
        ['eq', '$bucket', bucketName],
        // 精确匹配要上传的对象名
        ['eq', '$key', objectKey],
        // 限制上传文件大小 (0 到 500MB)
        ['content-length-range', 0, 500 * 1024 * 1024],
      ],
    };

    // 使用 ali-oss SDK 计算签名
    const formData = client.calculatePostSignature(policy);

    // 构建完整的OSS URL
    const fullUrl = `${host}/${objectKey}`;

    console.log('生成上传策略成功，目标URL：', fullUrl);

    // 返回上传策略信息，包含taskId
    return {
      code: 0,
      message: '获取上传策略成功',
      data: {
        taskId, // 任务ID，用于后续处理
        accessKeyId: formData.OSSAccessKeyId,
        host, // 上传的目标地址
        policy: formData.policy,
        signature: formData.Signature,
        key: objectKey, // 必须与policy中的key一致
        fullUrl: fullUrl,
        // 保存文件信息，用于后续处理
        fileInfo: {
          fileName: fileName,
          fileSize: fileSize,
          duration: duration,
          userId: userId
        }
      }
    };
    
  } catch (error) {
    console.error('get-oss-upload-policy 云函数执行错误：', error);
    
    return {
      code: 500,
      message: '服务器内部错误',
      error: error.message
    };
  }
};
