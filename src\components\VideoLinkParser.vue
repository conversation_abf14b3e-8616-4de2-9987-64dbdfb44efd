<template>
  <view class="video-link-parser">
    <view class="parser-header">
      <view class="parser-icon">🔗</view>
      <view class="parser-info">
        <text class="parser-title">视频链接解析</text>
        <text class="parser-desc">支持抖音、小红书视频链接，自动获取无水印版本</text>
      </view>
    </view>

    <view class="input-section">
      <view class="input-container">
        <textarea
          v-model="linkInput"
          class="link-input"
          placeholder="请粘贴抖音或小红书的分享内容（支持链接或完整分享文本）..."
          :disabled="isProcessing"
          @input="handleInputChange"
          @blur="handleInputBlur"
        />
        <view v-if="linkInput" class="clear-btn" @click="clearInput">
          <text class="clear-icon">✕</text>
        </view>
      </view>
      
      <view class="input-tips">
        <text class="tip-text">💡 支持的内容格式:</text>
        <text class="tip-item">• 抖音分享链接: https://v.douyin.com/xxx/</text>
        <text class="tip-item">• 抖音分享文本: 复制打开抖音，看看【用户名的作品】...</text>
        <text class="tip-item">• 小红书分享链接: https://xiaohongshu.com/xxx</text>
      </view>
    </view>

    <view class="action-section">
      <button 
        class="parse-btn"
        :class="{ 'btn-disabled': !canParse, 'btn-loading': isProcessing }"
        :disabled="!canParse || isProcessing"
        @click="handleParseClick"
      >
        <text v-if="!isProcessing" class="btn-text">解析视频</text>
        <view v-else class="loading-content">
          <text class="loading-icon">⏳</text>
          <text class="btn-text">{{ processingStatus }}</text>
        </view>
      </button>
    </view>

    <!-- 解析进度显示 -->
    <view v-if="isProcessing" class="progress-section">
      <view class="progress-steps">
        <view 
          v-for="(step, index) in progressSteps" 
          :key="index"
          class="progress-step"
          :class="{
            'step-active': index === currentStepIndex,
            'step-completed': index < currentStepIndex,
            'step-pending': index > currentStepIndex
          }"
        >
          <view class="step-indicator">
            <text v-if="index < currentStepIndex" class="step-icon">✓</text>
            <text v-else-if="index === currentStepIndex" class="step-loading">⏳</text>
            <text v-else class="step-number">{{ index + 1 }}</text>
          </view>
          <text class="step-text">{{ step }}</text>
        </view>
      </view>
    </view>

    <!-- 解析结果预览 -->
    <view v-if="parseResult" class="result-section">
      <view class="result-header">
        <text class="result-title">解析成功！</text>
        <text class="result-desc">视频信息已获取，点击确认继续处理</text>
      </view>
      
      <view class="video-preview">
        <view class="preview-info">
          <view class="info-row">
            <text class="info-label">标题:</text>
            <text class="info-value">{{ parseResult.title || '无标题' }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">时长:</text>
            <text class="info-value">{{ formatDuration(parseResult.duration) }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">大小:</text>
            <text class="info-value">{{ formatFileSize(parseResult.fileSize) }}</text>
          </view>
          <view class="info-row">
            <text class="info-label">来源:</text>
            <text class="info-value platform-tag" :class="parseResult.platform">
              {{ parseResult.platform === 'douyin' ? '抖音' : '小红书' }}
            </text>
          </view>
        </view>
      </view>

      <view class="result-actions">
        <button class="retry-btn" @click="resetParser">重新解析</button>
        <button class="confirm-btn" @click="confirmParsedVideo">确认使用</button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { formatFileSize, formatDuration } from '@/utils/common'

// Props
interface Props {
  disabled?: boolean
  userInfo?: {
    openid?: string
    userId?: string
    isLogin?: boolean
  }
}

const props = withDefaults(defineProps<Props>(), {
  disabled: false,
  userInfo: () => ({ isLogin: false })
})

// Emits
interface Emits {
  (e: 'parsed', result: ParsedVideoResult): void
  (e: 'error', error: string): void
}

const emit = defineEmits<Emits>()

// 解析结果接口
interface ParsedVideoResult {
  platform: 'douyin' | 'xiaohongshu'
  title: string
  duration: number
  fileSize: number
  videoUrl: string
  thumbnailUrl?: string
  originalLink: string
  task?: {
    taskId: string
    status: string
    platform: string
  }
}

// 响应式数据
const linkInput = ref('')
const isProcessing = ref(false)
const processingStatus = ref('')
const parseResult = ref<ParsedVideoResult | null>(null)
const currentStepIndex = ref(0)

// 解析步骤
const progressSteps = [
  '识别链接平台',
  '获取视频信息', 
  '下载无水印视频',
  '处理完成'
]

// 计算属性
const canParse = computed(() => {
  return linkInput.value.trim().length > 0 && !props.disabled && !isProcessing.value
})

// 检测链接平台（增强版，支持分享文本）
const detectPlatform = (inputText: string): 'douyin' | 'xiaohongshu' | null => {
  const cleanText = inputText.toLowerCase().trim()
  
  // 抖音检测特征
  const douyinIndicators = [
    '复制打开抖音',
    'v.douyin.com',
    'www.douyin.com',
    '抖音',
    'douyin'
  ]
  
  // 小红书检测特征  
  const xiaohongshuIndicators = [
    'xiaohongshu.com',
    'xhslink.com',
    '小红书',
    'redbook'
  ]
  
  // 检测抖音
  for (const indicator of douyinIndicators) {
    if (cleanText.includes(indicator)) {
      return 'douyin'
    }
  }
  
  // 检测小红书
  for (const indicator of xiaohongshuIndicators) {
    if (cleanText.includes(indicator)) {
      return 'xiaohongshu'
    }
  }
  
  return null
}

// 处理输入变化
const handleInputChange = () => {
  if (parseResult.value) {
    parseResult.value = null
  }
}

// 处理输入失焦
const handleInputBlur = () => {
  linkInput.value = linkInput.value.trim()
}

// 清空输入
const clearInput = () => {
  linkInput.value = ''
  parseResult.value = null
}

// 重置解析器
const resetParser = () => {
  linkInput.value = ''
  parseResult.value = null
  isProcessing.value = false
  processingStatus.value = ''
  currentStepIndex.value = 0
}

// 处理解析点击
const handleParseClick = async () => {
  if (!canParse.value) return
  
  // 检查用户登录状态
  if (!props.userInfo?.isLogin || (!props.userInfo?.openid && !props.userInfo?.userId)) {
    emit('error', '请先登录后再使用链接解析功能')
    return
  }
  
  const platform = detectPlatform(linkInput.value)
  if (!platform) {
    emit('error', '无法识别内容格式，请确认输入的是抖音或小红书的分享内容')
    return
  }
  
  await startParsing(platform)
}

// 开始解析
const startParsing = async (platform: 'douyin' | 'xiaohongshu') => {
  isProcessing.value = true
  currentStepIndex.value = 0
  processingStatus.value = '识别链接平台...'
  
  try {
    // 步骤1: 识别平台
    await sleep(500)
    currentStepIndex.value = 1
    processingStatus.value = '获取视频信息...'
    
    // 调用云函数解析视频
    const result = await uniCloud.callFunction({
      name: 'parse-video-link',
      data: {
        url: linkInput.value.trim(), // 发送完整的输入内容（可能是链接或分享文本）
        openid: props.userInfo?.openid,
        userId: props.userInfo?.userId,
        // platform参数移除，让云函数自动识别
      }
    })
    
    if (result.result.code !== 0) {
      throw new Error(result.result.message || '解析失败')
    }
    
    // 步骤2: 获取信息完成
    await sleep(500)
    currentStepIndex.value = 2
    processingStatus.value = '下载无水印视频...'
    
    // 模拟下载过程
    await sleep(2000)
    currentStepIndex.value = 3
    processingStatus.value = '处理完成'
    
    await sleep(500)
    
    // 设置解析结果 - 使用云函数返回的实际数据结构
    parseResult.value = {
      platform: result.result.data.platform,
      title: result.result.data.title,
      duration: 0, // 链接解析时无法获取准确时长
      fileSize: 0, // 链接解析时无法获取准确大小
      videoUrl: result.result.data.video.url,
      thumbnailUrl: result.result.data.video.cover,
      originalLink: linkInput.value.trim(),
      // 添加任务信息
      task: result.result.data.task
    }
    
  } catch (error: any) {
    console.error('解析视频链接失败:', error)
    emit('error', error.message || '解析失败，请检查链接是否正确')
  } finally {
    isProcessing.value = false
    processingStatus.value = ''
    currentStepIndex.value = 0
  }
}

// 确认使用解析的视频
const confirmParsedVideo = () => {
  if (parseResult.value) {
    emit('parsed', parseResult.value)
  }
}

// 辅助函数
const sleep = (ms: number) => new Promise(resolve => setTimeout(resolve, ms))

// 监听解析结果变化
watch(parseResult, (newResult) => {
  if (newResult) {
    console.log('视频解析完成:', newResult)
  }
})
</script>

<style scoped>
.video-link-parser {
  background: white;
  border-radius: 20rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.04);
  border: 1rpx solid #f3f4f6;
}

/* ==================== 头部区域 ==================== */
.parser-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f3f4f6;
}

.parser-icon {
  font-size: 40rpx;
  width: 56rpx;
  height: 56rpx;
  line-height: 56rpx;
  text-align: center;
  border-radius: 12rpx;
  background: linear-gradient(135deg, #e0f2fe 0%, #b3e5fc 100%);
}

.parser-info {
  flex: 1;
}

.parser-title {
  display: block;
  font-size: 32rpx;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4rpx;
}

.parser-desc {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  line-height: 1.4;
}

/* ==================== 输入区域 ==================== */
.input-section {
  margin-bottom: 24rpx;
}

.input-container {
  position: relative;
  margin-bottom: 16rpx;
}

.link-input {
  width: 100%;
  min-height: 120rpx;
  padding: 16rpx 48rpx 16rpx 16rpx;
  background: #f8fafc;
  border: 2rpx solid #e2e8f0;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #1f2937;
  line-height: 1.5;
  resize: none;
  transition: all 0.3s ease;
}

.link-input:focus {
  border-color: #3b82f6;
  background: white;
  box-shadow: 0 0 0 3rpx rgba(59, 130, 246, 0.1);
}

.link-input:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.clear-btn {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 28rpx;
  height: 28rpx;
  border-radius: 50%;
  background: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-btn:active {
  background: #374151;
  transform: scale(0.95);
}

.clear-icon {
  color: white;
  font-size: 20rpx;
  font-weight: bold;
}

.input-tips {
  background: #f0f9ff;
  border: 1rpx solid #e0f2fe;
  border-radius: 8rpx;
  padding: 12rpx 16rpx;
}

.tip-text {
  display: block;
  font-size: 24rpx;
  color: #0369a1;
  font-weight: 600;
  margin-bottom: 8rpx;
}

.tip-item {
  display: block;
  font-size: 22rpx;
  color: #0284c7;
  line-height: 1.4;
  margin-bottom: 4rpx;
}

/* ==================== 操作区域 ==================== */
.action-section {
  margin-bottom: 24rpx;
}

.parse-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  color: white;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 16rpx rgba(59, 130, 246, 0.3);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.parse-btn:not(.btn-disabled):active {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 20rpx rgba(59, 130, 246, 0.4);
}

.parse-btn.btn-disabled {
  background: #e5e7eb;
  color: #9ca3af;
  box-shadow: none;
  cursor: not-allowed;
}

.parse-btn.btn-loading {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  box-shadow: 0 4rpx 16rpx rgba(245, 158, 11, 0.3);
}

.loading-content {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.loading-icon {
  animation: rotate 2s linear infinite;
}

.btn-text {
  color: inherit;
}

/* ==================== 进度区域 ==================== */
.progress-section {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  border: 1rpx solid #e2e8f0;
}

.progress-steps {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.progress-step {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.step-indicator {
  width: 32rpx;
  height: 32rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.step-completed .step-indicator {
  background: #10b981;
  color: white;
}

.step-active .step-indicator {
  background: #f59e0b;
  color: white;
}

.step-pending .step-indicator {
  background: #e5e7eb;
  color: #6b7280;
}

.step-loading {
  animation: rotate 1s linear infinite;
}

.step-text {
  font-size: 26rpx;
  color: #1f2937;
}

.step-active .step-text {
  font-weight: 600;
  color: #f59e0b;
}

.step-completed .step-text {
  color: #10b981;
}

/* ==================== 结果区域 ==================== */
.result-section {
  border: 2rpx solid #10b981;
  border-radius: 12rpx;
  padding: 20rpx;
  background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.result-header {
  text-align: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid rgba(16, 185, 129, 0.2);
}

.result-title {
  display: block;
  font-size: 32rpx;
  font-weight: 700;
  color: #10b981;
  margin-bottom: 8rpx;
}

.result-desc {
  display: block;
  font-size: 24rpx;
  color: #047857;
}

.video-preview {
  margin-bottom: 20rpx;
}

.preview-info {
  background: white;
  border-radius: 8rpx;
  padding: 16rpx;
  border: 1rpx solid rgba(16, 185, 129, 0.2);
}

.info-row {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
  gap: 12rpx;
}

.info-row:last-child {
  margin-bottom: 0;
}

.info-label {
  font-size: 24rpx;
  color: #6b7280;
  min-width: 80rpx;
  font-weight: 500;
}

.info-value {
  font-size: 24rpx;
  color: #1f2937;
  flex: 1;
  font-weight: 500;
}

.platform-tag {
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: 600;
  color: white;
}

.platform-tag.douyin {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
}

.platform-tag.xiaohongshu {
  background: linear-gradient(135deg, #ff4757 0%, #ff3838 100%);
}

.result-actions {
  display: flex;
  gap: 12rpx;
}

.retry-btn {
  flex: 1;
  height: 72rpx;
  background: white;
  color: #6b7280;
  border: 2rpx solid #e5e7eb;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
}

.retry-btn:active {
  background: #f9fafb;
  border-color: #d1d5db;
}

.confirm-btn {
  flex: 2;
  height: 72rpx;
  background: linear-gradient(135deg, #10b981 0%, #059669 100%);
  color: white;
  border: none;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 600;
  box-shadow: 0 2rpx 8rpx rgba(16, 185, 129, 0.3);
}

.confirm-btn:active {
  transform: translateY(-1rpx);
  box-shadow: 0 4rpx 12rpx rgba(16, 185, 129, 0.4);
}

/* ==================== 动画效果 ==================== */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
</style>