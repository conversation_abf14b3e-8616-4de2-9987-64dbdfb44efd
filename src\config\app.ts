/**
 * 应用配置文件
 * 统一管理应用的各种配置项
 */

// 视频上传配置
export interface VideoUploadConfig {
  // 文件大小限制（字节）
  maxFileSize: number
  // 文件大小限制（MB，用于显示）
  maxFileSizeMB: number
  // 视频时长限制（秒）
  maxDuration: number
  // 支持的视频格式
  supportedFormats: string[]
  // 支持的视频格式描述
  supportedFormatsDesc: string
}

// 应用配置接口
export interface AppConfig {
  // 视频上传配置
  videoUpload: VideoUploadConfig
}

// 默认配置
export const defaultConfig: AppConfig = {
  videoUpload: {
    // 100MB = 100 * 1024 * 1024 字节
    maxFileSize: 100 * 1024 * 1024,
    maxFileSizeMB: 100,
    // 60秒
    maxDuration: 10*60,
    // 支持的视频格式
    supportedFormats: ['mp4', 'flv', 'avi', 'wmv'],
    // 格式描述
    supportedFormatsDesc: 'MP4、flv、AVI、WMV等主流视频格式'
  }
}

// 获取配置的函数
export const getAppConfig = (): AppConfig => {
  // 这里可以从本地存储、远程配置等地方获取配置
  // 目前先返回默认配置
  return defaultConfig
}

// 获取视频上传配置
export const getVideoUploadConfig = (): VideoUploadConfig => {
  return getAppConfig().videoUpload
}

// 格式化文件大小显示
export const formatMaxFileSize = (config: VideoUploadConfig): string => {
  return `${config.maxFileSizeMB}MB`
}

// 格式化时长显示
export const formatMaxDuration = (config: VideoUploadConfig): string => {
  if (config.maxDuration < 60) {
    return `${config.maxDuration}秒`
  } else {
    const minutes = Math.floor(config.maxDuration / 60)
    const seconds = config.maxDuration % 60
    if (seconds === 0) {
      return `${minutes}分钟`
    } else {
      return `${minutes}分${seconds}秒`
    }
  }
}

// 生成上传描述文本
export const generateUploadDesc = (config: VideoUploadConfig): string => {
  return `支持${config.supportedFormatsDesc}，最大${formatMaxFileSize(config)}，时长不超过${formatMaxDuration(config)}`
}

// 生成文件大小说明文本
export const generateFileSizeDesc = (config: VideoUploadConfig): string => {
  return `建议文件大小不超过${formatMaxFileSize(config)}`
}
