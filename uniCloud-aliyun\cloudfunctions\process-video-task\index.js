// uniCloud云函数：处理视频任务核心逻辑
"use strict";

const Core = require("@alicloud/pop-core");
const createConfig = require("uni-config-center");

/**
 * 处理视频任务的核心逻辑
 *
 * @param {Object} event
 * @param {string} event.taskId - 任务ID
 * @param {string} event.ossUrl - 视频OSS地址（用于音频提取）
 * @param {string} event.audioOssUrl - 音频文件地址（用于语音识别）
 * @param {string} event.action - 执行动作：extract_audio, speech_recognition, translate, merge_subtitle
 * @returns {Object} 处理结果
 */
exports.main = async (event, context) => {
  try {
    const { taskId, ossUrl, audioOssUrl, action } = event;

    console.log("process-video-task 云函数被调用，参数：", { taskId, ossUrl, audioOssUrl, action });

    // 参数验证
    if (!taskId) {
      return {
        code: 400,
        message: "缺少必要参数：taskId",
      };
    }

    if (!action) {
      return {
        code: 400,
        message: "缺少必要参数：action",
      };
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const tasksCollection = db.collection("tasks");

    // 根据动作执行相应的处理
    let result = null;
    switch (action) {
      case "extract_audio":
        if (!ossUrl) {
          throw new Error("音频提取需要ossUrl参数");
        }
        result = await extractAudio(taskId, ossUrl, tasksCollection);
        break;

      case "speech_recognition":
        if (!audioOssUrl) {
          throw new Error("语音识别需要audioOssUrl参数");
        }
        result = await speechRecognition(taskId, audioOssUrl, tasksCollection);
        break;

      case "translate":
        result = await translateSubtitle(taskId, tasksCollection);
        break;

      case "merge_subtitle":
        result = await mergeSubtitle(taskId, tasksCollection);
        break;

      case "upload_srt":
        if (!event.srtContent) {
          throw new Error("上传SRT需要srtContent参数");
        }
        result = await uploadSrtAndProceed(taskId, event.srtContent, tasksCollection);
        break;

      case "handle_paraformer_success":
        if (!event.paraformerResult) {
          throw new Error("处理Paraformer成功结果需要paraformerResult参数");
        }
        console.log("event值", event);
        result = await handleParaformerSuccess(taskId, event.paraformerResult, tasksCollection);
        break;

      default:
        throw new Error("不支持的动作：" + action);
    }

    return {
      code: 200,
      message: "任务处理成功",
      data: result,
    };
  } catch (error) {
    console.error("process-video-task 云函数执行错误：", error);

    // 更新任务状态为失败
    if (event.taskId) {
      try {
        const db = uniCloud.database();
        await db.collection("tasks").doc(event.taskId).update({
          status: "failed",
          errorMessage: error.message,
          updateTime: new Date(),
        });
      } catch (updateError) {
        console.error("更新任务失败状态时出错：", updateError);
      }
    }

    return {
      code: 500,
      message: "任务处理失败: " + error.message,
    };
  }
};

/**
 * 提取音频功能
 * @param {string} taskId - 任务ID
 * @param {string} ossUrl - 视频OSS地址
 * @param {Object} tasksCollection - 任务集合
 */
async function extractAudio(taskId, ossUrl, tasksCollection) {
  console.log("开始音频提取，taskId：", taskId, "，ossUrl：", ossUrl);

  // 验证任务是否存在且状态正确
  const taskInfo = await tasksCollection.doc(taskId).get();
  if (!taskInfo.data || taskInfo.data.length === 0) {
    throw new Error("任务不存在");
  }

  if (taskInfo.data[0].status !== "extracting_audio") {
    console.warn("任务状态不正确，当前状态：", taskInfo.data[0].status);
  }

  // 获取阿里云MPS配置
  const aliyunConfig = createConfig({
    pluginId: "aliyun-mps",
    defaultConfig: {
      regionId: "cn-shanghai",
    },
  });

  const accessKeyId = aliyunConfig.config("accessKeyId");
  const accessKeySecret = aliyunConfig.config("accessKeySecret");
  const regionId = aliyunConfig.config("regionId");
  const pipelineId = aliyunConfig.config("pipelineId");
  const inputBucket = aliyunConfig.config("inputBucket");
  const outputBucket = aliyunConfig.config("outputBucket");
  const audioTemplateId = aliyunConfig.config("audioTemplateId");

  if (!accessKeyId || !accessKeySecret) {
    throw new Error("阿里云MPS配置错误，请配置访问密钥");
  }

  if (!pipelineId || !inputBucket || !outputBucket) {
    throw new Error("阿里云MPS配置不完整，请配置pipelineId、inputBucket和outputBucket");
  }

  console.log("MPS配置检查通过，regionId：", regionId, "，pipelineId：", pipelineId);

  // 创建MPS客户端
  const mpsClient = new Core({
    accessKeyId,
    accessKeySecret,
    endpoint: `https://mts.${regionId}.aliyuncs.com`,
    apiVersion: "2014-06-18",
  });

  // 从OSS URL中解析bucket和object信息
  const ossUrlParts = ossUrl.match(/https:\/\/([^.]+)\.([^.]+)\.aliyuncs\.com\/(.+)/);
  if (!ossUrlParts) {
    throw new Error("无效的OSS URL格式: " + ossUrl);
  }

  const [, bucketName, region, objectPath] = ossUrlParts;
  console.log("解析OSS URL:", { bucketName, region, objectPath });

  // 构建MPS转码参数 - 仅提取音频为MP3格式
  const mpsParams = {
    Input: JSON.stringify({
      Location: `oss-${regionId}`,
      Bucket: bucketName,
      Object: objectPath, // 直接使用OSS中的文件路径
    }),
    OutputBucket: outputBucket,
    OutputLocation: `oss-${regionId}`,
    Outputs: JSON.stringify([
      {
        OutputObject: `audio/${taskId}.mp3`,
        TemplateId: audioTemplateId || "S00000001-100010", // 使用配置的模板ID或默认音频模板
        UserData: JSON.stringify({ taskId }),
        // 确保音频提取的容器格式正确
        Container: JSON.stringify({
          Format: "mp3"
        }),
        // 明确指定只保留音频流，移除视频流
        Video: JSON.stringify({
          Remove: "true"
        }),
      },
    ]),
    PipelineId: pipelineId,
  };

  console.log("调用MPS SubmitJobs API，参数：", mpsParams);

  // 调用MPS API提交转码任务
  const mpsResponse = await mpsClient.request("SubmitJobs", mpsParams, {
    method: "POST",
  });

  console.log("MPS API 响应：", mpsResponse);

  const { JobResultList } = mpsResponse;
  if (JobResultList && JobResultList.JobResult && JobResultList.JobResult.length > 0) {
    const jobResult = JobResultList.JobResult[0];
    const { Job } = jobResult;

    if (Job && Job.JobId) {
      console.log("音频提取任务提交成功，JobId：", Job.JobId);
      // 更新任务记录，保存MPS JobId
      await tasksCollection.doc(taskId).update({
        mpsJobId: Job.JobId,
        updateTime: new Date(),
      });

      return {
        jobId: Job.JobId,
        status: "submitted",
      };
    } else {
      throw new Error("MPS任务提交失败：" + JSON.stringify(jobResult));
    }
  } else {
    throw new Error("MPS API响应格式错误");
  }
}

/**
 * 语音识别功能 - 使用阿里云Paraformer服务
 * @param {string} taskId - 任务ID
 * @param {string} audioOssUrl - 音频文件地址
 * @param {Object} tasksCollection - 任务集合
 */
async function speechRecognition(taskId, audioOssUrl, tasksCollection) {
  console.log("开始语音识别，taskId：", taskId, "，audioOssUrl：", audioOssUrl);

  // 验证任务是否存在且状态正确
  const taskInfo = await tasksCollection.doc(taskId).get();
  if (!taskInfo.data || taskInfo.data.length === 0) {
    throw new Error("任务不存在");
  }

  if (taskInfo.data[0].status !== "recognizing") {
    console.warn("任务状态不正确，当前状态：", taskInfo.data[0].status);
  }

  // 获取阿里云DashScope配置
  const aliyunConfig = createConfig({
    pluginId: "aliyun-dashscope",
    defaultConfig: {
      endpoint: "https://dashscope.aliyuncs.com",
      model: "paraformer-v2",
    },
  });

  const apiKey = aliyunConfig.config("apiKey");
  const endpoint = aliyunConfig.config("endpoint");
  const model = aliyunConfig.config("model");

  // 验证必需的配置
  if (!apiKey) {
    throw new Error("阿里云DashScope配置缺失，请检查apiKey");
  }

  console.log("Paraformer配置检查通过，endpoint：", endpoint, "，model：", model);

  try {
    // 获取任务的源语言设置
    const taskData = taskInfo.data[0];
    const sourceLanguage = taskData.sourceLanguage || 'auto';

    // 提交Paraformer识别任务
    const paraformerTaskId = await submitParaformerTask(apiKey, endpoint, model, audioOssUrl, sourceLanguage);

    // 更新任务记录，保存Paraformer TaskId
    await tasksCollection.doc(taskId).update({
      paraformerTaskId: paraformerTaskId,
      updateTime: new Date(),
    });

    // 任务提交成功，状态将由定时触发器自动检查
    console.log("Paraformer任务提交成功，状态将由定时触发器自动检查");

    return {
      taskId: paraformerTaskId,
      status: "submitted",
      message: "语音识别任务提交成功",
    };
  } catch (error) {
    console.error("Paraformer API调用失败：", error);

    // 更新任务状态为失败
    await tasksCollection.doc(taskId).update({
      status: "failed",
      errorMessage: "语音识别失败：" + error.message,
      updateTime: new Date(),
    });

    throw error;
  }
}

/**
 * 处理Paraformer识别成功的结果
 * @param {string} taskId - 任务ID
 * @param {string} result - 识别结果
 * @param {Object} tasksCollection - 任务集合
 */
async function handleParaformerSuccess(taskId, result, tasksCollection) {
  try {
    console.log("处理Paraformer识别成功结果，taskId：", taskId);

    // Paraformer返回的是句子数组，需要转换为SRT格式
    const srtContent = convertParaformerResultToSRT(result);

    if (!srtContent) {
      throw new Error("Paraformer识别结果转换SRT失败");
    }

    console.log("SRT转换完成，字幕长度：", srtContent.length);

    // 直接上传SRT文件，不再进行语言检测和翻译
    await uniCloud.callFunction({
      name: "process-video-task",
      data: {
        taskId: taskId,
        action: "upload_srt",
        srtContent: srtContent,
      },
    });
  } catch (error) {
    console.error("处理Paraformer识别成功结果失败：", error);

    // 更新任务状态为失败
    await tasksCollection.doc(taskId).update({
      status: "failed",
      errorMessage: "处理识别结果失败：" + error.message,
      updateTime: new Date(),
    });

    throw error;
  }
}

/**
 * 从OSS下载SRT文件内容
 * @param {string} ossUrl - OSS文件地址
 * @returns {Promise<string>} SRT文件内容
 */
async function downloadSrtFromOss(ossUrl) {
  const https = require("https");
  const http = require("http");
  const url = require("url");

  return new Promise((resolve, reject) => {
    const parsedUrl = url.parse(ossUrl);
    const client = parsedUrl.protocol === "https:" ? https : http;

    const req = client.get(ossUrl, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        if (res.statusCode === 200) {
          resolve(data);
        } else {
          reject(new Error(`下载SRT文件失败，状态码: ${res.statusCode}`));
        }
      });
    });

    req.on("error", (error) => {
      reject(new Error(`下载SRT文件失败: ${error.message}`));
    });
  });
}

/**
 * 翻译字幕功能
 * @param {string} taskId - 任务ID
 * @param {Object} tasksCollection - 任务集合
 */
async function translateSubtitle(taskId, tasksCollection) {
  console.log("开始翻译字幕，taskId：", taskId);

  try {
    // 获取任务信息
    const taskInfo = await tasksCollection.doc(taskId).get();
    if (!taskInfo.data || taskInfo.data.length === 0) {
      throw new Error("任务不存在");
    }

    const task = taskInfo.data[0];
    const { sourceLanguage = "auto", targetLanguage = "zh", subtitleOssUrl } = task;

    if (!subtitleOssUrl) {
      throw new Error("缺少字幕文件地址");
    }

    console.log(`开始翻译：${sourceLanguage} -> ${targetLanguage}`);

    // 从OSS下载字幕文件内容
    const srtContent = await downloadSrtFromOss(subtitleOssUrl);
    if (!srtContent) {
      throw new Error("无法下载字幕文件");
    }

    // 解析SRT字幕
    const subtitleEntries = parseSRT(srtContent);
    console.log(`解析到 ${subtitleEntries.length} 条字幕`);

    // 获取阿里云机器翻译配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-mt",
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const regionId = aliyunConfig.config("regionId");

    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云机器翻译配置缺失");
    }

    // 创建机器翻译客户端
    const mtClient = new Core({
      accessKeyId,
      accessKeySecret,
      endpoint: `https://mt.${regionId}.aliyuncs.com`,
      apiVersion: "2018-10-12",
    });

    // 批量翻译字幕文本
    const translatedEntries = [];
    const batchSize = 10; // 每批处理10条字幕

    for (let i = 0; i < subtitleEntries.length; i += batchSize) {
      const batch = subtitleEntries.slice(i, i + batchSize);
      const batchTexts = batch.map((entry) => entry.text);

      console.log(`翻译第 ${Math.floor(i / batchSize) + 1} 批，共 ${batch.length} 条`);

      try {
        // 调用阿里云机器翻译API
        const translateResponse = await mtClient.request(
          "TranslateGeneral",
          {
            FormatType: "text",
            SourceLanguage: sourceLanguage === 'auto' ? 'auto' : sourceLanguage,
            TargetLanguage: targetLanguage,
            SourceText: batchTexts.join("\n"),
            Scene: "general",
          },
          {
            method: "POST",
          }
        );

        if (translateResponse.Data && translateResponse.Data.Translated) {
          const translatedTexts = translateResponse.Data.Translated.split("\n");

          // 将翻译结果与原字幕条目合并
          for (let j = 0; j < batch.length; j++) {
            translatedEntries.push({
              ...batch[j],
              text: translatedTexts[j] || batch[j].text, // 如果翻译失败，使用原文
            });
          }
        } else {
          // 翻译失败，使用原文
          translatedEntries.push(...batch);
        }

        // 避免API调用过于频繁
        await new Promise((resolve) => setTimeout(resolve, 100));
      } catch (error) {
        console.error(`翻译第 ${Math.floor(i / batchSize) + 1} 批失败：`, error);
        // 翻译失败，使用原文
        translatedEntries.push(...batch);
      }
    }

    // 重新生成SRT格式
    const translatedSrtContent = generateSRT(translatedEntries);
    console.log("翻译完成，生成新的SRT字幕");

    // 上传翻译后的SRT文件并进行字幕烧录
    await uploadTranslatedSrtAndMerge(taskId, translatedSrtContent, tasksCollection);

    return {
      status: "completed",
      message: "翻译完成",
      translatedCount: translatedEntries.length,
    };
  } catch (error) {
    console.error("翻译字幕失败：", error);

    // 更新任务状态为失败
    await tasksCollection.doc(taskId).update({
      status: "failed",
      errorMessage: "翻译失败：" + error.message,
      updateTime: new Date(),
    });

    throw error;
  }
}

/**
 * 合并字幕功能 - 使用阿里云MPS进行字幕烧录
 * @param {string} taskId - 任务ID
 * @param {Object} tasksCollection - 任务集合
 */
async function mergeSubtitle(taskId, tasksCollection) {
  console.log("开始字幕烧录，taskId：", taskId);

  try {
    // 获取任务信息
    const taskInfo = await tasksCollection.doc(taskId).get();
    if (!taskInfo.data || taskInfo.data.length === 0) {
      throw new Error("任务不存在");
    }

    const task = taskInfo.data[0];
    const { ossUrl, subtitleOssUrl } = task;

    console.log("任务数据：", JSON.stringify(task, null, 2));

    if (!ossUrl) {
      throw new Error("缺少原始视频文件地址");
    }

    if (!subtitleOssUrl) {
      throw new Error("缺少字幕文件地址");
    }

    console.log("开始字幕烧录，原视频：", ossUrl, "，字幕文件：", subtitleOssUrl);

    // 获取阿里云MPS配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-mps",
      defaultConfig: {
        regionId: "cn-shanghai",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const regionId = aliyunConfig.config("regionId");
    const pipelineId = aliyunConfig.config("pipelineId");
    const outputBucket = aliyunConfig.config("outputBucket");

    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云MPS配置错误，请配置访问密钥");
    }

    if (!pipelineId || !outputBucket) {
      throw new Error("阿里云MPS配置不完整，请配置pipelineId和outputBucket");
    }

    console.log("MPS配置检查通过，regionId：", regionId, "，pipelineId：", pipelineId);

    // 创建MPS客户端
    const mpsClient = new Core({
      accessKeyId,
      accessKeySecret,
      endpoint: `https://mts.${regionId}.aliyuncs.com`,
      apiVersion: "2014-06-18",
    });

    // 解析原视频OSS URL
    const videoOssUrlParts = ossUrl.match(/https:\/\/([^.]+)\.([^.]+)\.aliyuncs\.com\/(.+)/);
    if (!videoOssUrlParts) {
      throw new Error("无效的视频OSS URL格式: " + ossUrl);
    }

    const [, videoBucketName, videoRegion, videoObjectPath] = videoOssUrlParts;
    console.log("解析视频OSS URL:", { videoBucketName, videoRegion, videoObjectPath });

    // 解析字幕OSS URL
    const subtitleOssUrlParts = subtitleOssUrl.match(
      /https?:\/\/([^.]+)\.([^.]+)\.aliyuncs\.com\/(.+)/
    );
    if (!subtitleOssUrlParts) {
      throw new Error("无效的字幕OSS URL格式: " + subtitleOssUrl);
    }

    const [, subtitleBucketName, subtitleRegion, subtitleObjectPath] = subtitleOssUrlParts;
    console.log("解析字幕OSS URL:", { subtitleBucketName, subtitleRegion, subtitleObjectPath });

    // 验证解析结果
    if (!subtitleBucketName || !subtitleObjectPath) {
      throw new Error(`字幕OSS URL解析失败: Bucket=${subtitleBucketName}, Object=${subtitleObjectPath}, URL=${subtitleOssUrl}`);
    }

    // 构建MPS字幕烧录参数
    const mpsParams = {
      Input: JSON.stringify({
        Location: `oss-${regionId}`,
        Bucket: videoBucketName,
        Object: videoObjectPath,
      }),
      OutputBucket: outputBucket,
      OutputLocation: `oss-${regionId}`,
      Outputs: JSON.stringify([
        {
          OutputObject: `final/${taskId}.mp4`,
          TemplateId: "d963d203a57042d9a49c730fc61d2b44", // 使用系统预置的转码模板
          UserData: JSON.stringify({
            taskId: taskId,
            action: "burn_subtitle",
          }),
          // 字幕配置应该在这里，按照官方文档的正确结构
          SubtitleConfig: JSON.stringify({
            ExtSubtitleList: [
              {
                Input: {
                  Location: `oss-${regionId}`,
                  Bucket: subtitleBucketName,
                  Object: subtitleObjectPath,
                },
                CharEnc: "UTF-8", // 字符编码
                FontSize: 4,
              },
            ],
          }),
        },
      ]),
      PipelineId: pipelineId,
    };

    console.log("调用MPS SubmitJobs API进行字幕烧录，参数：", JSON.stringify(mpsParams, null, 2));

    // 额外调试：显示解析后的Outputs内容
    const outputsObj = JSON.parse(mpsParams.Outputs);
    console.log("Outputs对象详情：", JSON.stringify(outputsObj, null, 2));

    // 调用MPS API提交字幕烧录任务
    const mpsResponse = await mpsClient.request("SubmitJobs", mpsParams, {
      method: "POST",
    });

    console.log("MPS字幕烧录API响应：", mpsResponse);

    const { JobResultList } = mpsResponse;
    if (JobResultList && JobResultList.JobResult && JobResultList.JobResult.length > 0) {
      const jobResult = JobResultList.JobResult[0];
      const { Job } = jobResult;

      if (Job && Job.JobId) {
        console.log("字幕烧录任务提交成功，JobId：", Job.JobId);

        // 更新任务记录，保存MPS JobId
        await tasksCollection.doc(taskId).update({
          mpsJobId: Job.JobId,
          updateTime: new Date(),
        });

        return {
          jobId: Job.JobId,
          status: "submitted",
          message: "字幕烧录任务提交成功",
        };
      } else {
        throw new Error("MPS字幕烧录任务提交失败：" + JSON.stringify(jobResult));
      }
    } else {
      throw new Error("MPS API响应格式错误");
    }
  } catch (error) {
    console.error("字幕烧录失败：", error);

    // 更新任务状态为失败
    await tasksCollection.doc(taskId).update({
      status: "failed",
      errorMessage: "字幕烧录失败：" + error.message,
      updateTime: new Date(),
    });

    throw error;
  }
}

/**
 * 解析SRT字幕格式
 * @param {string} srtContent - SRT字幕内容
 * @returns {Array} 解析后的字幕条目数组
 */
function parseSRT(srtContent) {
  const entries = [];
  const blocks = srtContent.trim().split(/\n\s*\n/);

  for (const block of blocks) {
    const lines = block.trim().split("\n");
    if (lines.length >= 3) {
      const index = parseInt(lines[0]);
      const timeRange = lines[1];
      const text = lines.slice(2).join("\n");

      entries.push({
        index,
        timeRange,
        text: text.trim(),
      });
    }
  }

  return entries;
}

/**
 * 生成SRT字幕格式
 * @param {Array} entries - 字幕条目数组
 * @returns {string} SRT格式字符串
 */
function generateSRT(entries) {
  return entries
    .map((entry) => {
      return `${entry.index}\n${entry.timeRange}\n${entry.text}\n`;
    })
    .join("\n");
}

/**
 * 上传SRT文件并继续下一步处理（翻译）
 * @param {string} taskId - 任务ID
 * @param {string} srtContent - SRT内容
 * @param {Object} tasksCollection - 任务集合
 */
async function uploadSrtAndProceed(taskId, srtContent, tasksCollection) {
  const OSS = require("ali-oss");

  try {
    // 获取阿里云OSS配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-oss",
      defaultConfig: {
        region: "oss-cn-shanghai",
        bucket: "video--tanslate",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const region = aliyunConfig.config("region");
    const bucketName = aliyunConfig.config("bucket");

    // 验证配置
    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云OSS配置缺失");
    }

    // 初始化OSS客户端
    const client = new OSS({
      accessKeyId: accessKeyId,
      accessKeySecret: accessKeySecret,
      bucket: bucketName,
      region: region,
    });

    // 生成SRT文件的OSS路径
    const timestamp = Date.now();
    const objectKey = `subtitle/task_${taskId}_${timestamp}.srt`;

    // 上传SRT内容到OSS
    const uploadResult = await client.put(objectKey, Buffer.from(srtContent, "utf8"), {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
      },
    });

    const subtitleOssUrl = uploadResult.url;
    console.log("上传SRT文件完成，地址：", subtitleOssUrl);

    // 更新任务状态为translating，准备翻译
    await tasksCollection.doc(taskId).update({
      status: "translating",
      subtitleOssUrl: subtitleOssUrl,
      updateTime: new Date(),
    });

    // 检查任务是否处于后台模式
    const taskInfo = await tasksCollection.doc(taskId).get();
    const isBackgroundMode = taskInfo.data && taskInfo.data[0] && taskInfo.data[0].backgroundMode;

    if (isBackgroundMode) {
      // 后台模式：通过task-scheduler自动处理
      console.log(`任务 ${taskId} 处于后台模式，将由调度器自动处理翻译`);
      return {
        status: "translating",
        message: "已进入翻译阶段，后台处理中",
        subtitleOssUrl: subtitleOssUrl
      };
    } else {
      // 前台模式：立即调用翻译功能
      const translateResult = await uniCloud.callFunction({
        name: "process-video-task",
        data: {
          taskId: taskId,
          action: "translate",
        },
      });

      console.log("调用翻译功能结果：", translateResult);
      return translateResult.result;
    }
  } catch (error) {
    console.error("上传SRT文件失败：", error);

    // 更新任务状态为失败
    await tasksCollection.doc(taskId).update({
      status: "failed",
      errorMessage: "上传字幕文件失败：" + error.message,
      updateTime: new Date(),
    });

    throw error;
  }
}

/**
 * 上传翻译后的SRT文件并进行字幕烧录
 * @param {string} taskId - 任务ID
 * @param {string} srtContent - 翻译后的SRT内容
 * @param {Object} tasksCollection - 任务集合
 */
async function uploadTranslatedSrtAndMerge(taskId, srtContent, tasksCollection) {
  const OSS = require("ali-oss");

  try {
    // 获取阿里云OSS配置
    const aliyunConfig = createConfig({
      pluginId: "aliyun-oss",
      defaultConfig: {
        region: "oss-cn-shanghai",
        bucket: "video--tanslate",
      },
    });

    const accessKeyId = aliyunConfig.config("accessKeyId");
    const accessKeySecret = aliyunConfig.config("accessKeySecret");
    const region = aliyunConfig.config("region");
    const bucketName = aliyunConfig.config("bucket");

    // 验证配置
    if (!accessKeyId || !accessKeySecret) {
      throw new Error("阿里云OSS配置缺失");
    }

    // 初始化OSS客户端
    const client = new OSS({
      accessKeyId: accessKeyId,
      accessKeySecret: accessKeySecret,
      bucket: bucketName,
      region: region,
    });

    // 生成翻译后SRT文件的OSS路径
    const timestamp = Date.now();
    const objectKey = `subtitle/task_${taskId}_translated_${timestamp}.srt`;

    // 上传翻译后的SRT内容到OSS
    const uploadResult = await client.put(objectKey, Buffer.from(srtContent, "utf8"), {
      headers: {
        "Content-Type": "text/plain; charset=utf-8",
      },
    });

    const subtitleOssUrl = uploadResult.url;
    console.log("上传翻译后SRT文件完成，地址：", subtitleOssUrl);

    // 更新任务状态为merging，准备字幕烧录
    await tasksCollection.doc(taskId).update({
      status: "merging",
      subtitleOssUrl: subtitleOssUrl,
      updateTime: new Date(),
    });

    // 检查任务是否处于后台模式
    const taskInfo = await tasksCollection.doc(taskId).get();
    const isBackgroundMode = taskInfo.data && taskInfo.data[0] && taskInfo.data[0].backgroundMode;

    if (isBackgroundMode) {
      // 后台模式：通过task-scheduler自动处理
      console.log(`任务 ${taskId} 处于后台模式，将由调度器自动处理字幕烧录`);
      return {
        status: "merging",
        message: "已进入字幕烧录阶段，后台处理中",
        subtitleOssUrl: subtitleOssUrl
      };
    } else {
      // 前台模式：立即调用字幕烧录功能
      const mergeResult = await uniCloud.callFunction({
        name: "process-video-task",
        data: {
          taskId: taskId,
          action: "merge_subtitle",
        },
      });

      console.log("调用字幕烧录功能结果：", mergeResult);
      return mergeResult.result;
    }
  } catch (error) {
    console.error("上传翻译后SRT文件失败：", error);

    // 更新任务状态为失败
    await tasksCollection.doc(taskId).update({
      status: "failed",
      errorMessage: "上传翻译后字幕文件失败：" + error.message,
      updateTime: new Date(),
    });

    throw error;
  }
}

/**
 * 将Paraformer识别结果转换为SRT格式
 * @param {Object|string} paraformerResult - Paraformer识别结果
 * @returns {string} SRT格式字符串
 */
function convertParaformerResultToSRT(paraformerResult) {
  console.log("paraformerResult:", paraformerResult);
  try {
    // 处理输入数据：如果是字符串则解析，如果已经是对象则直接使用
    let resultData;
    if (typeof paraformerResult === "string") {
      resultData = JSON.parse(paraformerResult);
    } else {
      resultData = paraformerResult;
    }

    // 检查数据结构：期望包含 transcripts 数组
    if (!resultData || !resultData.transcripts || !Array.isArray(resultData.transcripts)) {
      console.warn("Paraformer结果格式不正确：缺少transcripts数组");
      console.log("实际数据结构：", JSON.stringify(resultData, null, 2));
      return null;
    }

    const transcripts = resultData.transcripts;

    if (transcripts.length === 0) {
      console.warn("Paraformer结果为空：没有识别到转录内容");
      return null;
    }

    // 获取第一个音轨的句子数据
    const firstTranscript = transcripts[0];
    if (!firstTranscript.sentences || !Array.isArray(firstTranscript.sentences)) {
      console.warn("Paraformer结果格式不正确：缺少sentences数组");
      return null;
    }

    const sentences = firstTranscript.sentences;

    if (sentences.length === 0) {
      console.warn("Paraformer结果为空：没有识别到句子");
      return null;
    }

    console.log(`开始转换 ${sentences.length} 个句子为SRT格式`);

    let srtContent = "";

    sentences.forEach((sentence, index) => {
      // 使用Paraformer的字段名：begin_time, end_time, text
      const { begin_time, end_time, text } = sentence;

      if (begin_time === undefined || end_time === undefined || !text) {
        console.warn(`句子 ${index + 1} 数据不完整：`, sentence);
        return; // 跳过这个句子
      }

      // 转换时间格式（毫秒转SRT时间格式）
      const startTime = formatSRTTime(begin_time);
      const endTime = formatSRTTime(end_time);

      srtContent += `${index + 1}\n`;
      srtContent += `${startTime} --> ${endTime}\n`;
      srtContent += `${text}\n\n`;
    });

    console.log("SRT转换完成，内容长度：", srtContent.length);
    return srtContent.trim();
  } catch (error) {
    console.error("转换Paraformer结果为SRT失败：", error);
    console.error("输入数据：", paraformerResult);
    return null;
  }
}

/**
 * 格式化SRT时间格式
 * @param {number} timeMs - 毫秒时间
 * @returns {string} SRT时间格式 (HH:MM:SS,mmm)
 */
function formatSRTTime(timeMs) {
  const totalSeconds = Math.floor(timeMs / 1000);
  const milliseconds = timeMs % 1000;

  const hours = Math.floor(totalSeconds / 3600);
  const minutes = Math.floor((totalSeconds % 3600) / 60);
  const seconds = totalSeconds % 60;

  return `${hours.toString().padStart(2, "0")}:${minutes.toString().padStart(2, "0")}:${seconds
    .toString()
    .padStart(2, "0")},${milliseconds.toString().padStart(3, "0")}`;
}

/**
 * 提交Paraformer语音识别任务
 * @param {string} apiKey - API密钥
 * @param {string} endpoint - API端点
 * @param {string} model - 模型名称
 * @param {string} audioOssUrl - 音频文件URL
 * @param {string} sourceLanguage - 源语言代码
 * @returns {Promise<string>} 任务ID
 */
async function submitParaformerTask(apiKey, endpoint, model, audioOssUrl, sourceLanguage = 'auto') {
  const https = require("https");
  const http = require("http");
  const url = require("url");

  // 构建请求参数，包含语言设置
  const requestData = {
    model: model,
    input: {
      file_urls: [audioOssUrl],
    },
    parameters: {
      channel_id: [0],
      disfluency_removal_enabled: false,
      timestamp_alignment_enabled: false,
    },
  };

  // 如果指定了源语言且不是自动识别，则添加语言参数
  if (sourceLanguage && sourceLanguage !== 'auto') {
    // 将语言代码映射为Paraformer支持的格式
    const languageMap = {
      'zh': 'zh-cn',
      'en': 'en',
      'ja': 'ja',
      'ko': 'ko',
      'de': 'de',
      'fr': 'fr',
      'ru': 'ru'
    };

    const paraformerLanguage = languageMap[sourceLanguage];
    if (paraformerLanguage) {
      requestData.parameters.language = paraformerLanguage;
      console.log(`设置Paraformer识别语言为: ${paraformerLanguage}`);
    }
  }

  const requestBody = JSON.stringify(requestData);
  const parsedUrl = url.parse(`${endpoint}/api/v1/services/audio/asr/transcription`);

  const options = {
    hostname: parsedUrl.hostname,
    port: parsedUrl.port || (parsedUrl.protocol === "https:" ? 443 : 80),
    path: parsedUrl.path,
    method: "POST",
    headers: {
      Authorization: `Bearer ${apiKey}`,
      "Content-Type": "application/json",
      "X-DashScope-Async": "enable",
      "Content-Length": Buffer.byteLength(requestBody),
    },
  };

  return new Promise((resolve, reject) => {
    const client = parsedUrl.protocol === "https:" ? https : http;

    const req = client.request(options, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        try {
          const response = JSON.parse(data);

          if (res.statusCode === 200 && response.output && response.output.task_id) {
            console.log("Paraformer任务提交成功，TaskId：", response.output.task_id);
            resolve(response.output.task_id);
          } else {
            console.error("Paraformer任务提交失败：", response);
            reject(new Error(`Paraformer任务提交失败: ${JSON.stringify(response)}`));
          }
        } catch (error) {
          console.error("解析Paraformer响应失败：", error);
          reject(new Error(`解析响应失败: ${error.message}`));
        }
      });
    });

    req.on("error", (error) => {
      console.error("Paraformer请求失败：", error);
      reject(new Error(`请求失败: ${error.message}`));
    });

    req.write(requestBody);
    req.end();
  });
}

/**
 * 查询Paraformer语音识别任务状态
 * @param {string} apiKey - API密钥
 * @param {string} endpoint - API端点
 * @param {string} taskId - 任务ID
 * @returns {Promise<Object>} 任务状态和结果
 */
async function queryParaformerTask(apiKey, endpoint, taskId) {
  const https = require("https");
  const http = require("http");
  const url = require("url");

  const parsedUrl = url.parse(`${endpoint}/api/v1/tasks/${taskId}`);

  const options = {
    hostname: parsedUrl.hostname,
    port: parsedUrl.port || (parsedUrl.protocol === "https:" ? 443 : 80),
    path: parsedUrl.path,
    method: "GET",
    headers: {
      Authorization: `Bearer ${apiKey}`,
      "Content-Type": "application/json",
    },
  };

  return new Promise((resolve, reject) => {
    const client = parsedUrl.protocol === "https:" ? https : http;

    const req = client.request(options, (res) => {
      let data = "";

      res.on("data", (chunk) => {
        data += chunk;
      });

      res.on("end", () => {
        try {
          const response = JSON.parse(data);

          if (res.statusCode === 200) {
            resolve(response);
          } else {
            console.error("查询Paraformer任务失败：", response);
            reject(new Error(`查询任务失败: ${JSON.stringify(response)}`));
          }
        } catch (error) {
          console.error("解析Paraformer查询响应失败：", error);
          reject(new Error(`解析响应失败: ${error.message}`));
        }
      });
    });

    req.on("error", (error) => {
      console.error("Paraformer查询请求失败：", error);
      reject(new Error(`请求失败: ${error.message}`));
    });

    req.end();
  });
}
