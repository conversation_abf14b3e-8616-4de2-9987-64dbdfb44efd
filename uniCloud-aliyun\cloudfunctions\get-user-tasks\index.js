// uniCloud云函数：获取用户历史任务记录
"use strict";

/**
 * 获取用户的历史任务记录（最近7天）
 *
 * @param {Object} event
 * @param {string} event.openid - 用户openid（必需）
 * @param {string} event.startDate - 开始日期（可选，默认为7天前）
 * @param {string} event.status - 任务状态筛选（可选，不传则返回所有状态）
 * @returns {Object} 用户历史任务列表
 */
exports.main = async (event, context) => {
  try {
    const { openid, startDate, status } = event;
    const { CLIENTUA, CLIENTIP } = context;

    console.log("get-user-tasks 云函数被调用，参数：", { 
      openid: openid ? openid.substring(0, 8) + '***' : null, 
      startDate, 
      status 
    });

    // 参数验证
    if (!openid) {
      console.error("参数验证失败：缺少openid参数");
      return {
        errCode: "INVALID_PARAM",
        errMsg: "缺少必要参数：openid",
        code: 400
      };
    }

    // 验证openid格式（简单验证）
    if (typeof openid !== "string" || openid.length === 0) {
      console.error("参数验证失败：openid格式不正确");
      return {
        errCode: "INVALID_PARAM",
        errMsg: "openid格式不正确",
        code: 400
      };
    }

    // 获取数据库引用
    const db = uniCloud.database();
    const usersCollection = db.collection("users");
    const tasksCollection = db.collection("tasks");

    console.log("开始验证用户身份...");

    // 验证用户是否存在
    const userResult = await usersCollection
      .where({
        openid: openid,
      })
      .field({
        _id: true,
        status: true
      })
      .limit(1)
      .get();

    if (!userResult.data || userResult.data.length === 0) {
      console.error("用户验证失败：用户不存在", openid.substring(0, 8) + '***');
      return {
        errCode: "USER_NOT_FOUND",
        errMsg: "用户不存在",
        code: 404
      };
    }

    const userData = userResult.data[0];
    const userId = userData._id;

    // 检查用户状态
    if (userData.status === 'banned') {
      console.error("用户验证失败：用户已被封禁", userId);
      return {
        errCode: "USER_BANNED",
        errMsg: "用户已被封禁",
        code: 403
      };
    }

    console.log("用户验证通过，userId：", userId);

    // 计算查询时间范围（默认最近7天）
    let queryStartDate;
    if (startDate) {
      queryStartDate = new Date(startDate);
    } else {
      queryStartDate = new Date();
      queryStartDate.setDate(queryStartDate.getDate() - 7);
    }

    console.log("查询时间范围：", queryStartDate.toISOString(), "至今");

    // 构建查询条件
    let queryCondition = {
      userId: userId,
      createTime: db.command.gte(queryStartDate),
      // 过滤掉已删除的任务
      status: db.command.neq('deleted')
    };

    // 如果指定了状态筛选
    if (status && status !== 'all') {
      // 验证状态值是否有效
      const validStatuses = ['uploading', 'extracting_audio', 'recognizing', 'translating', 'merging', 'completed', 'failed', 'cancelled'];
      if (validStatuses.includes(status)) {
        // 同时满足指定状态和非删除状态
        queryCondition.status = db.command.and([
          db.command.eq(status),
          db.command.neq('deleted')
        ]);
        console.log("添加状态筛选：", status);
      } else {
        console.warn("无效的状态筛选值：", status);
      }
    }

    // 查询用户的历史任务
    console.log("开始查询历史任务...");
    const tasksResult = await tasksCollection
      .where(queryCondition)
      .field({
        _id: true,
        fileName: true,
        fileSize: true,
        duration: true,
        status: true,
        createTime: true,
        updateTime: true,
        finalVideoUrl: true,
        errorMessage: true,
        ossUrl: true
      })
      .orderBy('createTime', 'desc') // 按创建时间倒序排列
      .limit(100) // 限制最多返回100条记录
      .get();

    const tasks = tasksResult.data || [];
    console.log(`查询完成，找到 ${tasks.length} 条历史任务`);

    // 格式化返回数据
    const formattedTasks = tasks.map(task => {
      const formattedTask = {
        taskId: task._id,
        fileName: task.fileName || '未知文件',
        fileSize: task.fileSize || 0,
        duration: task.duration || 0,
        status: task.status,
        createdAt: task.createTime,
        updatedAt: task.updateTime,
        ossUrl: task.ossUrl || ''
      };

      // 根据状态添加额外信息
      if (task.status === 'completed' && task.finalVideoUrl) {
        formattedTask.finalVideoUrl = task.finalVideoUrl;
      }

      if (task.status === 'failed' && task.errorMessage) {
        formattedTask.errorMessage = task.errorMessage;
      }

      // 添加进度信息（用于前端显示）
      formattedTask.progress = getProgressByStatus(task.status);

      return formattedTask;
    });

    // 更新用户最后活跃时间
    try {
      await usersCollection
        .where({ _id: userId })
        .update({
          lastActiveTime: new Date(),
          lastActiveIP: CLIENTIP,
          lastActiveUA: CLIENTUA
        });
    } catch (updateError) {
      console.warn("更新用户活跃时间失败：", updateError.message);
      // 不影响主要功能，继续执行
    }

    return {
      errCode: 0,
      code: 200,
      errMsg: "查询成功",
      data: formattedTasks,
      meta: {
        total: formattedTasks.length,
        queryRange: {
          startDate: queryStartDate.toISOString(),
          endDate: new Date().toISOString()
        },
        statusFilter: status || 'all'
      }
    };

  } catch (error) {
    console.error("get-user-tasks 云函数执行错误：", error);

    return {
      errCode: "GET_USER_TASKS_FAILED",
      code: 500,
      errMsg: "获取历史任务失败: " + error.message,
    };
  }
};

/**
 * 根据任务状态返回进度百分比
 * @param {string} status 任务状态
 * @returns {number} 进度百分比
 */
function getProgressByStatus(status) {
  const progressMap = {
    uploading: 10,
    extracting_audio: 25,
    recognizing: 50,
    translating: 75,
    merging: 90,
    completed: 100,
    failed: 0,
  };

  return progressMap[status] || 0;
}
